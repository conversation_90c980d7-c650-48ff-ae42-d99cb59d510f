#!/usr/bin/env python3
"""
Fix Corrupted PnL Script
Resets the total_profit_sol that was corrupted by failed transactions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager
from state_manager import StateManager

def main():
    print("🔧 FIXING CORRUPTED PNL FROM FAILED TRANSACTIONS")
    print("=" * 60)
    
    # Initialize managers
    config = ConfigManager()
    state = StateManager(config)
    
    # Show current state
    stats = state.get_stats()
    print(f"\n📊 CURRENT STATE:")
    print(f"   Starting SOL: {stats['starting_sol']:.4f}")
    print(f"   Available SOL: {stats['current_available_sol']:.4f}")
    print(f"   Total Profit SOL: {stats['total_profit_sol']:.4f}")
    print(f"   Total Trades: {stats['total_trades']}")
    print(f"   Win Rate: {stats['winrate_percent']:.1f}%")
    print(f"   Open Positions: {stats['open_positions_count']}")
    
    # Check for open positions
    open_positions = state.get_open_positions()
    if open_positions:
        print(f"\n⚠️  WARNING: {len(open_positions)} open positions found:")
        for token, pos in open_positions.items():
            print(f"   • {token[:16]}... - {pos.get('sol_amount', 0):.4f} SOL")
        print("   These positions will be preserved during PnL reset")
    
    # Ask for confirmation
    print(f"\n🔧 PROPOSED FIX:")
    print(f"   • Reset total_profit_sol from {stats['total_profit_sol']:.4f} to 0.0000")
    print(f"   • Recalculate available_sol based on actual positions")
    print(f"   • Preserve all open positions and trade counts")
    print(f"   • This fixes corruption from failed transactions with fake signatures")
    
    response = input(f"\n❓ Proceed with PnL reset? (y/N): ").strip().lower()
    
    if response == 'y':
        print(f"\n🔧 Resetting corrupted PnL...")
        state.reset_corrupted_pnl()
        
        # Show new state
        new_stats = state.get_stats()
        print(f"\n✅ PNL RESET COMPLETE:")
        print(f"   Starting SOL: {new_stats['starting_sol']:.4f}")
        print(f"   Available SOL: {new_stats['current_available_sol']:.4f}")
        print(f"   Total Profit SOL: {new_stats['total_profit_sol']:.4f}")
        print(f"   Total Trades: {new_stats['total_trades']}")
        print(f"   Win Rate: {new_stats['winrate_percent']:.1f}%")
        print(f"   Open Positions: {new_stats['open_positions_count']}")
        
        print(f"\n🎉 SUCCESS: Your wallet balance should now accurately reflect reality!")
        print(f"💡 Future trades will only update PnL for verified transactions")
        
    else:
        print(f"\n❌ PnL reset cancelled")
        print(f"💡 Run this script again when ready to fix the corruption")

if __name__ == "__main__":
    main()
