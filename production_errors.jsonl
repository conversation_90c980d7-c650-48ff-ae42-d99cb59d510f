{"timestamp": "2025-06-22T10:06:56.128459+00:00", "error_type": "main", "error_message": "Failed to fetch SOL price (attempt 1): HTTPSConnectionPool(host='api.coingecko.com', port=443): Read timed out. (read timeout=8)", "module": "main", "function": "get_sol_price", "severity": "error", "context": {"filename": "main.py", "lineno": 283, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:08.388139+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 9689.286680", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 523, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:08.392088+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 524, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:08.394945+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:14.040188+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 9689.286680", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 523, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:14.041903+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 524, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:14.044639+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:19.450940+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 9621.849245", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 523, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:19.451625+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-22T10:10:19.452209+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 524, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:19.452775+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-22T10:10:19.454503+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:27.795710+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 7687.719312", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 542, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:27.798066+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 543, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:27.799141+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump [Context: CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:31.096006+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 7687.719312", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 542, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:31.096803+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 543, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:31.097541+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump [Context: CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:37.192668+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 7687.719312", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 542, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:37.193501+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T07:43:37.194171+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 543, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:37.194648+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T07:43:37.195493+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump [Context: CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:43.979364+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 3588.247741", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 533, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:43.981691+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 534, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:43.982513+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:47.400890+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 3588.247741", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 533, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:47.401677+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 534, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:47.402708+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:52.642890+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 734, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:52.643708+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 900, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:56.109048+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:59.303972+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:59.305176+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:14.775536+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:18.213386+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:21.710683+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:21.712125+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:23.565282+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:27.226740+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:27.227361+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:18:30.668451+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:30.669200+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:18:30.671266+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:41.736376+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 1565.759076", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 537, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:41.737024+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 538, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:41.738122+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump [Context: B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.621956+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c PumpPortal API Error Details:", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 831, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.622598+00:00", "error_type": "pumpportal_trader", "error_message": "   Status Code: 400", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 832, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.623371+00:00", "error_type": "pumpportal_trader", "error_message": "   Response Text: Bad Request", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 833, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.624027+00:00", "error_type": "pumpportal_trader", "error_message": "   Request URL: https://pumpportal.fun/api/trade-local", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 834, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.624539+00:00", "error_type": "pumpportal_trader", "error_message": "   Request Payload: {'publicKey': '75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA', 'action': 'sell', 'mint': 'B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump', 'amount': '100%', 'denominatedInSol': 'false', 'slippage': 29, 'priorityFee': 0.0001, 'pool': 'auto'}", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 835, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.625089+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.832552+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c PumpPortal API Error Details:", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 831, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.833166+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.833949+00:00", "error_type": "pumpportal_trader", "error_message": "   Status Code: 400", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 832, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.834444+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 7 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.834883+00:00", "error_type": "pumpportal_trader", "error_message": "   Response Text: Bad Request", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 833, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.835270+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 8 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.835692+00:00", "error_type": "pumpportal_trader", "error_message": "   Request URL: https://pumpportal.fun/api/trade-local", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 834, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.836036+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 9 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.836550+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 5 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.836932+00:00", "error_type": "pumpportal_trader", "error_message": "   Request Payload: {'publicKey': '75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA', 'action': 'sell', 'mint': 'B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump', 'amount': '100%', 'denominatedInSol': 'false', 'slippage': 34, 'priorityFee': 0.0001, 'pool': 'auto'}", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 835, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.837441+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 10 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.837847+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 7 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:52.062575+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:52.063339+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 8 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:52.065005+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump [Context: B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:52.065509+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 9 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:53.930683+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:53.931411+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 10 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:57.386959+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:57.387693+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 11 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:48:00.584028+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:48:00.584673+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 12 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:48:00.585816+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump [Context: B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:48:00.586280+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 13 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:04:03.485979+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 140576.829219", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:03.486656+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:03.487853+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for Csi6JSousB25racCdiueRMBFX9srJzUQFnEKoGJqpump [Context: Csi6JSousB25racCdiueRMBFX9srJzUQFnEKoGJqpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:09.211214+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 5so6ckYB9haVnvUH8xyGYvMGwY5teqc6fqiYUZhobbKaT8rNPV9J5bhzNcwrMwHPuQbtNzB43BNzJRpiB4s6WJtx - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 761, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:09.213086+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 5so6ckYB9haVnvUH8xyGYvMGwY5teqc6fqiYUZhobbKaT8rNPV9J5bhzNcwrMwHPuQbtNzB43BNzJRpiB4s6WJtx", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 927, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:26:44.194330+00:00", "error_type": "asyncio", "error_message": "Task was destroyed but it is pending!\ntask: <Task pending name='analysis_worker_0' coro=<AnalysisWorkerPool._worker_loop() running at c:\\Users\\<USER>\\Downloads\\Mainnet 230625\\analysis_worker_pool.py:307> wait_for=<Future pending cb=[Task.task_wakeup()]>>", "module": "base_events", "function": "default_exception_handler", "severity": "error", "context": {"filename": "base_events.py", "lineno": 1865, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:26:44.196678+00:00", "error_type": "asyncio", "error_message": "Task was destroyed but it is pending!\ntask: <Task pending name='analysis_worker_1' coro=<AnalysisWorkerPool._worker_loop() running at c:\\Users\\<USER>\\Downloads\\Mainnet 230625\\analysis_worker_pool.py:307> wait_for=<Future pending cb=[Task.task_wakeup()]>>", "module": "base_events", "function": "default_exception_handler", "severity": "error", "context": {"filename": "base_events.py", "lineno": 1865, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:26:44.198186+00:00", "error_type": "asyncio", "error_message": "Task was destroyed but it is pending!\ntask: <Task pending name='analysis_worker_2' coro=<AnalysisWorkerPool._worker_loop() running at c:\\Users\\<USER>\\Downloads\\Mainnet 230625\\analysis_worker_pool.py:307> wait_for=<Future pending cb=[Task.task_wakeup()]>>", "module": "base_events", "function": "default_exception_handler", "severity": "error", "context": {"filename": "base_events.py", "lineno": 1865, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:27:02.984395+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 284679.487493", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:27:02.988470+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:27:02.998628+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for CBRCSQSf9Ybfn1KyFAMK2Rs64bxR58d9Gq7hQTPBpump [Context: CBRCSQSf9Ybfn1KyFAMK2Rs64bxR58d9Gq7hQTPBpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:27:09.418058+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 2xFLRaeBxvAA32JSiquLDeAqEZBumF97EKxGDXLeGE3YxSSfWmusac9iHUg1yBnkc1z3QVb8vZvsREcuWZG9tJi2 - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 761, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:27:09.420225+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 2xFLRaeBxvAA32JSiquLDeAqEZBumF97EKxGDXLeGE3YxSSfWmusac9iHUg1yBnkc1z3QVb8vZvsREcuWZG9tJi2", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 927, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:02.245198+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 27730.212548", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:02.247696+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:02.251906+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 8auRc8yWqf1QdrRJazxrKvoPkD788YxgBnCV2LEbpump [Context: 8auRc8yWqf1QdrRJazxrKvoPkD788YxgBnCV2LEbpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:06.955877+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c PumpPortal API Error Details:", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 854, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:06.958134+00:00", "error_type": "pumpportal_trader", "error_message": "   Status Code: 400", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 855, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:06.960093+00:00", "error_type": "pumpportal_trader", "error_message": "   Response Text: Bad Request", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 856, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:06.962489+00:00", "error_type": "pumpportal_trader", "error_message": "   Request URL: https://pumpportal.fun/api/trade-local", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 857, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:06.964721+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:32:06.968597+00:00", "error_type": "pumpportal_trader", "error_message": "   Request Payload: {'publicKey': '75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA', 'action': 'sell', 'mint': '8auRc8yWqf1QdrRJazxrKvoPkD788YxgBnCV2LEbpump', 'amount': '100%', 'denominatedInSol': 'false', 'slippage': 29, 'priorityFee': 0.0001, 'pool': 'auto'}", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 858, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:06.972906+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:32:55.971953+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 344875.606314", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:55.975252+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:32:55.984933+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 2g4YHtqbnZPpEVrwNEBAtUxrYHeiFkgxeNYfvrWRpump [Context: 2g4YHtqbnZPpEVrwNEBAtUxrYHeiFkgxeNYfvrWRpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:33:02.575296+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 3q1w5LNYvUMZv8i4Y9kPM31Y4EZnwhjtvSu43PhfvyBSpPMyeqnPFiqkt8gaXtEZR5yVGrxU5TQ95TyspgFCYxWn - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 761, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:33:02.577695+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 3q1w5LNYvUMZv8i4Y9kPM31Y4EZnwhjtvSu43PhfvyBSpPMyeqnPFiqkt8gaXtEZR5yVGrxU5TQ95TyspgFCYxWn", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 927, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:33:02.582532+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:34:25.958553+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 307360.229685", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:34:25.960218+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:34:25.961917+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:34:25.963716+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:34:25.968323+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 5 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:34:25.974868+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 6jYxPGiAR4ck1oMVQfjR7gxzyDCwEgHULqkkvUk2pump [Context: 6jYxPGiAR4ck1oMVQfjR7gxzyDCwEgHULqkkvUk2pump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:34:25.976823+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 6 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:34:32.318717+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 2z5Xm6Wii454xJAsFNPT7RhCCibJQVU1DbnkRWEhxAHqJwFEt1R8oUahzwnimSkEjHCkhqevTx48kATeV3Ld9BpR - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 761, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:34:32.321238+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 7 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:34:32.323491+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 2z5Xm6Wii454xJAsFNPT7RhCCibJQVU1DbnkRWEhxAHqJwFEt1R8oUahzwnimSkEjHCkhqevTx48kATeV3Ld9BpR", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 927, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:34:32.325698+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 7 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:34:32.328050+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 9 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:43.883828+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 339830.614109", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:35:43.886457+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 7 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:43.889570+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 11 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:43.892520+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:35:43.895293+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 8 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:43.898235+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 13 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:43.902066+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for AkgGv4nqv4YpUC49muE2zMfZCefsjuRMUxCRYeAPpump [Context: AkgGv4nqv4YpUC49muE2zMfZCefsjuRMUxCRYeAPpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:35:43.904000+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 14 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:50.157346+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 4d1XZnLX4yPhUxJReFF9xGnULRxy5sPhTrnc5F4xXYiDs1XWV3ouC6PjHumtDFeZ5q9rsDUQ5vZPrRX8x5vonmBL - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 761, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:35:50.160165+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 15 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:50.163165+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 4d1XZnLX4yPhUxJReFF9xGnULRxy5sPhTrnc5F4xXYiDs1XWV3ouC6PjHumtDFeZ5q9rsDUQ5vZPrRX8x5vonmBL", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 927, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:35:50.165796+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 8 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:35:50.168742+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 17 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.132514+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 253052.572776", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:38:42.133714+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.134804+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 HIGH ERROR RATE: 51 errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 124, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.136102+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 19 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.137982+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:38:42.139836+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.141007+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 HIGH ERROR RATE: 55 errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 124, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.142261+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 22 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.145988+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for FgmSLfiX4QCq6jcgNXAru6EJgyFiavpGGjdYy3KZpump [Context: FgmSLfiX4QCq6jcgNXAru6EJgyFiavpGGjdYy3KZpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:38:42.147262+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 HIGH ERROR RATE: 58 errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 124, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:42.150102+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 24 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:48.764314+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: cehv3sXR1nT3kjFg2cgJT3ENqc9NKYRcC8Jo15LRyeoTxrgVqaH6dqCvj1496imJE6houAhnZtFJavPki5D6d6L - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 761, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:38:48.766315+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 HIGH ERROR RATE: 61 errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 124, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:48.768048+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 26 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:48.769897+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: cehv3sXR1nT3kjFg2cgJT3ENqc9NKYRcC8Jo15LRyeoTxrgVqaH6dqCvj1496imJE6houAhnZtFJavPki5D6d6L", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 927, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:38:48.771520+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 HIGH ERROR RATE: 64 errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 124, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:38:48.773177+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 28 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
