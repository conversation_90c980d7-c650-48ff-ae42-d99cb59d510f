import json
import os
from typing import Dict, Any, Optional, List
import time
import logging
import asyncio
from datetime import datetime, timezone
import gzip
import shutil
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class StateManager:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.positions: Dict[str, Dict[str, Any]] = {}  # token -> position data
        self.trade_history: List[Dict[str, Any]] = []  # List of all trades
        self.token_last_trade_time: Dict[str, float] = {}  # token -> timestamp
        self.token_cooldowns: Dict[str, float] = {}  # token -> cooldown end time
        self.agent_limits: Dict[str, float] = {}  # agent -> remaining limit

        # POSITION LOCKING - Prevent race conditions during position updates
        self.position_locks: Dict[str, asyncio.Lock] = {}  # token -> lock
        # SURGICAL FIX: Enhanced deadlock detection and recovery
        self.position_lock_timeout = 8.0  # seconds to wait for lock acquisition (increased from 5s)
        self.lock_retry_attempts = 3  # number of retry attempts for failed locks
        self.active_locks: Dict[str, Dict[str, Any]] = {}  # track who holds what lock
        self.lock_wait_queue: Dict[str, Dict[str, Any]] = {}  # track who's waiting for locks

        # Read/Write lock separation for better concurrency
        self.read_locks: Dict[str, asyncio.Lock] = {}  # token -> read lock
        self.write_locks: Dict[str, asyncio.Lock] = {}  # token -> write lock

        # Moonbags feature has been removed as requested
        self.total_profit_sol = 0.0
        self.total_profit_usd = 0.0
        self.trades_count = 0
        self.win_count = 0
        self.loss_count = 0
        self.max_drawdown = 0.0
        self.current_strategy = "default"
        self.last_state_save = 0
        self.state_save_interval = 300  # Save state every 5 minutes
        # CRITICAL FIX: Use consistent state file path for real trading only
        self.state_file_path = 'sessions/trading_real_session.session'

        # Additional attributes for status display
        self.total_trades = 0
        self.winning_trades = 0
        self.total_profit = 0.0

        self.starting_sol = 0.0
        self.available_sol = 0.0

        # Add sol_per_trade to state for persistence
        self.sol_per_trade = 0.0

        # OPTIMIZED: Cache for external fees to avoid repeated config loading
        self._external_fees_cache = None
        self._external_fees_cache_time = 0
        self._cache_duration = 300  # 5 minutes

        self.load_state()

        if self.starting_sol <= 0:
            # Use total_sol_capital from trading_settings for real trading
            trading_settings = self.config_manager.get_trading_settings()
            self.starting_sol = trading_settings.get('total_sol_capital', 8.0)  # Use total_sol_capital from config
            self.available_sol = self.starting_sol
            logger.info(f"Initialized starting SOL from trading_settings.total_sol_capital: {self.starting_sol}")
        else:
            # Check if loaded starting_sol is outdated (less than configured total_sol_capital)
            trading_settings = self.config_manager.get_trading_settings()
            configured_capital = trading_settings.get('total_sol_capital', 8.0)

            if self.starting_sol < configured_capital:
                logger.warning(f"Loaded starting SOL ({self.starting_sol}) is less than configured total_sol_capital ({configured_capital})")
                logger.info(f"Updating starting SOL to match configuration: {configured_capital}")

                # Calculate the difference to add to available SOL
                capital_increase = configured_capital - self.starting_sol
                self.starting_sol = configured_capital
                self.available_sol += capital_increase

                logger.info(f"Updated: starting_sol={self.starting_sol}, available_sol={self.available_sol}")
            else:
                logger.info(f"Loaded starting SOL from state: {self.starting_sol}")
                self._recalculate_available_sol()

    def _recalculate_available_sol(self):
        sol_in_positions = sum(pos.get('sol_amount', 0) for pos in self.positions.values() if pos.get('status') == 'OPEN')
        self.available_sol = self.starting_sol - sol_in_positions + self.total_profit_sol
        logger.info(f"Recalculated available SOL: {self.available_sol:.4f}")

    def _calculate_external_fees(self, sol_amount: float, operation: str) -> float:
        """
        OPTIMIZED: Centralized external fee calculation with caching

        Args:
            sol_amount: Amount of SOL for the transaction
            operation: 'buy' or 'sell'

        Returns:
            Total external fees in SOL
        """
        current_time = time.time()

        # Check cache validity
        if (self._external_fees_cache is None or
            current_time - self._external_fees_cache_time > self._cache_duration):

            try:
                # SURGICAL FIX: Load config once and cache it - correct path for transaction_settings
                with open('finalconfig.json', 'r') as f:
                    config_data = json.load(f)
                transaction_settings = config_data.get('transaction_settings', {})

                self._external_fees_cache = {
                    'buy_tip_sol': transaction_settings.get('buy_tip_sol', 0.0001),  # Fixed: 0.1 milliSOL not 5 milliSOL
                    'gas_price_sol': transaction_settings.get('gas_price_sol', 0.0001),  # Fixed: 0.1 milliSOL not 5 milliSOL
                    'handling_fee_percent': transaction_settings.get('handling_fee_percent', 1.0),
                    'platform_fee_percent': transaction_settings.get('platform_fee_percent', 1.0)
                }
                self._external_fees_cache_time = current_time
                logger.debug("External fees cache updated")

            except Exception as e:
                logger.error(f"Error loading external fees from config: {e}")
                # Fallback fees
                self._external_fees_cache = {
                    'buy_tip_sol': 0.005,
                    'gas_price_sol': 0.005,
                    'handling_fee_percent': 1.0,
                    'platform_fee_percent': 1.0
                }

        fees = self._external_fees_cache

        if operation == 'buy':
            # Buy fees: tip + gas + percentage fees on SOL amount
            handling_fee_sol = sol_amount * (fees['handling_fee_percent'] / 100.0)
            platform_fee_sol = sol_amount * (fees['platform_fee_percent'] / 100.0)
            total_fees = fees['buy_tip_sol'] + fees['gas_price_sol'] + handling_fee_sol + platform_fee_sol

            logger.info(f"[MONEY] BUY FEES: Tip: {fees['buy_tip_sol']:.4f}, Gas: {fees['gas_price_sol']:.4f}, "
                       f"Handling: {handling_fee_sol:.4f}, Platform: {platform_fee_sol:.4f}, Total: {total_fees:.4f} SOL")

        elif operation == 'sell':
            # Sell fees: only gas fee (percentage fees deducted from proceeds)
            total_fees = fees['gas_price_sol']
            logger.info(f"[MONEY] SELL FEES: Gas: {total_fees:.4f} SOL")

        else:
            logger.error(f"Unknown operation: {operation}")
            total_fees = 0.01  # Fallback

        return total_fees

    def can_trade(self, token: str, cooldown_seconds: float) -> bool:
        """Check if a token can be traded based on cooldown period"""
        current_time = time.time()

        if token in self.token_cooldowns:
            cooldown_end = self.token_cooldowns[token]
            if current_time < cooldown_end:
                logger.debug(f"Cooldown active for {token} until {datetime.fromtimestamp(cooldown_end)}")
                return False
            else:
                del self.token_cooldowns[token]

        if token in self.token_last_trade_time:
            last_trade = self.token_last_trade_time[token]
            if current_time - last_trade < cooldown_seconds:
                logger.debug(f"Cooldown active for {token} based on last trade time.")
                return False

        return True

    def mark_traded(self, token: str, cooldown_seconds: float):
        """Mark a token as traded and set cooldown"""
        current_time = time.time()
        self.token_last_trade_time[token] = current_time
        self.token_cooldowns[token] = current_time + cooldown_seconds
        logger.debug(f"Marked {token} as traded, cooldown ends at {datetime.fromtimestamp(self.token_cooldowns[token])}")

    def _get_position_lock(self, token: str) -> asyncio.Lock:
        """Get or create a lock for a specific position (legacy method)"""
        if token not in self.position_locks:
            self.position_locks[token] = asyncio.Lock()
        return self.position_locks[token]

    def _get_read_lock(self, token: str) -> asyncio.Lock:
        """Get or create a read lock for a specific position"""
        if token not in self.read_locks:
            self.read_locks[token] = asyncio.Lock()
        return self.read_locks[token]

    def _get_write_lock(self, token: str) -> asyncio.Lock:
        """Get or create a write lock for a specific position"""
        if token not in self.write_locks:
            self.write_locks[token] = asyncio.Lock()
        return self.write_locks[token]

    async def _acquire_position_lock(self, token: str) -> bool:
        """Acquire position lock with timeout"""
        lock = self._get_position_lock(token)
        try:
            await asyncio.wait_for(lock.acquire(), timeout=self.position_lock_timeout)
            return True
        except asyncio.TimeoutError:
            logger.error(f"Failed to acquire position lock for {token} within {self.position_lock_timeout}s")
            return False

    def _release_position_lock(self, token: str):
        """Release position lock"""
        lock = self.position_locks.get(token)
        if lock and lock.locked():
            lock.release()
            logger.debug(f"Released position lock for {token}")

    @asynccontextmanager
    async def safe_position_lock(self, token: str, operation: str = "unknown"):
        """Context manager with deadlock detection and automatic recovery"""
        lock = self._get_position_lock(token)
        acquired = False
        start_time = time.time()
        task_id = id(asyncio.current_task()) if asyncio.current_task() else 0

        try:
            # Record lock attempt
            self.lock_wait_queue[token] = {
                'operation': operation,
                'start_time': start_time,
                'task_id': task_id
            }

            # Try to acquire with timeout and deadlock detection
            await asyncio.wait_for(lock.acquire(), timeout=self.position_lock_timeout)
            acquired = True

            # Record successful acquisition
            self.active_locks[token] = {
                'operation': operation,
                'acquired_time': time.time(),
                'task_id': task_id
            }

            # Remove from wait queue
            self.lock_wait_queue.pop(token, None)

            logger.debug(f"Acquired position lock for {token} ({operation})")
            yield

        except asyncio.TimeoutError:
            # DEADLOCK DETECTION: Check for circular waits
            logger.error(f"DEADLOCK DETECTED: {operation} on {token} timed out after {self.position_lock_timeout}s")
            self._handle_deadlock(token, operation)
            raise Exception(f"Deadlock detected for {token} during {operation}")

        except Exception as e:
            logger.error(f"Error acquiring position lock for {token}: {e}")
            raise

        finally:
            if acquired:
                self.active_locks.pop(token, None)
                lock.release()
                logger.debug(f"Released position lock for {token} ({operation})")
            self.lock_wait_queue.pop(token, None)

    def _handle_deadlock(self, token: str, operation: str):
        """Handle detected deadlock with recovery"""
        logger.error(f"DEADLOCK RECOVERY: Forcing unlock for {token} during {operation}")

        # Log current lock state for debugging
        if token in self.active_locks:
            active_lock = self.active_locks[token]
            logger.error(f"Current lock holder: {active_lock['operation']} (task {active_lock['task_id']})")

        # Force release the lock
        if token in self.position_locks:
            lock = self.position_locks[token]
            if lock.locked():
                try:
                    lock.release()
                    logger.warning(f"Force-released lock for {token}")
                except Exception as e:
                    logger.error(f"Error force-releasing lock for {token}: {e}")

        # Clear tracking data
        self.active_locks.pop(token, None)
        self.lock_wait_queue.pop(token, None)

        # Create new lock to ensure clean state
        self.position_locks[token] = asyncio.Lock()
        logger.info(f"Created new lock for {token} after deadlock recovery")

    @asynccontextmanager
    async def safe_position_read_lock(self, token: str, operation: str = "read"):
        """Context manager for read-only operations (multiple reads can happen simultaneously)"""
        lock = self._get_read_lock(token)
        acquired = False
        start_time = time.time()

        try:
            # Try to acquire with timeout and retry logic
            for attempt in range(self.lock_retry_attempts):
                try:
                    await asyncio.wait_for(lock.acquire(), timeout=self.position_lock_timeout)
                    acquired = True
                    logger.debug(f"Acquired read lock for {token} ({operation})")
                    yield
                    break
                except asyncio.TimeoutError:
                    if attempt < self.lock_retry_attempts - 1:
                        wait_time = 0.5 * (2 ** attempt)  # Exponential backoff
                        logger.warning(f"Read lock timeout for {token}, retrying in {wait_time}s (attempt {attempt + 1}/{self.lock_retry_attempts})")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"Read lock failed for {token} after {self.lock_retry_attempts} attempts")
                        raise Exception(f"Read lock timeout for {token} during {operation}")
        finally:
            if acquired:
                lock.release()
                logger.debug(f"Released read lock for {token} ({operation})")

    @asynccontextmanager
    async def safe_position_write_lock(self, token: str, operation: str = "write"):
        """Context manager for write operations (exclusive access required)"""
        lock = self._get_write_lock(token)
        acquired = False
        start_time = time.time()

        try:
            # Try to acquire with timeout and retry logic
            for attempt in range(self.lock_retry_attempts):
                try:
                    await asyncio.wait_for(lock.acquire(), timeout=self.position_lock_timeout)
                    acquired = True
                    logger.debug(f"Acquired write lock for {token} ({operation})")
                    yield
                    break
                except asyncio.TimeoutError:
                    if attempt < self.lock_retry_attempts - 1:
                        wait_time = 0.5 * (2 ** attempt)  # Exponential backoff
                        logger.warning(f"Write lock timeout for {token}, retrying in {wait_time}s (attempt {attempt + 1}/{self.lock_retry_attempts})")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"Write lock failed for {token} after {self.lock_retry_attempts} attempts")
                        raise Exception(f"Write lock timeout for {token} during {operation}")
        finally:
            if acquired:
                lock.release()
                logger.debug(f"Released write lock for {token} ({operation})")

    async def safe_position_update(self, token: str, update_func, *args, **kwargs):
        """Safely update position with enhanced deadlock detection"""
        async with self.safe_position_lock(token, "position_update"):
            return update_func(*args, **kwargs)

    async def open_position(self, token: str, entry_price: float, sol_amount: float, strategy: str = "default", metadata: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Open a new position with deadlock-safe locking, update available SOL, return position data."""
        async with self.safe_position_write_lock(token, "open_position"):
            if sol_amount > self.available_sol:
                logger.warning(f"Attempted to buy {sol_amount:.4f} SOL of {token}, but only {self.available_sol:.4f} available. Adjusting.")
                sol_amount = self.available_sol
            if sol_amount <= 0:
                logger.warning(f"Attempted to open position for {token} with zero or negative SOL ({sol_amount}). Skipping.")
                return None

            # OPTIMIZED: Calculate external fees using centralized config access
            total_external_fees = self._calculate_external_fees(sol_amount, 'buy')

            # SURGICAL FIX: Atomic balance validation and deduction to prevent race conditions
            total_cost = sol_amount + total_external_fees

            # CRITICAL: Atomic check and deduct operation
            if self.available_sol < total_cost:
                logger.error(f"CRITICAL: Attempted to deduct {total_cost:.4f} SOL but only {self.available_sol:.4f} available!")
                logger.error(f"  Position: {sol_amount:.4f} SOL + Fees: {total_external_fees:.4f} SOL = {total_cost:.4f} SOL")
                logger.error(f"  This should have been caught by balance validation in bot_controller!")
                # Prevent negative balance by adjusting to available amount
                if self.available_sol > total_external_fees:
                    logger.warning(f"Adjusting position to available balance: {self.available_sol:.4f} SOL")
                    sol_amount = max(0, self.available_sol - total_external_fees)
                    total_cost = sol_amount + total_external_fees
                else:
                    logger.error(f"Insufficient SOL even for fees - this should not happen!")
                    return None

            # ATOMIC: Deduct both trade amount AND external fees from available SOL
            old_balance = self.available_sol
            self.available_sol -= total_cost

            # SURGICAL FIX: Ensure available_sol never goes negative with detailed logging
            if self.available_sol < 0:
                logger.error(f"CRITICAL: Available SOL went negative: {self.available_sol:.4f}!")
                logger.error(f"Position details - Token: {token}, SOL amount: {sol_amount}, Entry price: {entry_price}")
                logger.error(f"Total external fees: {total_external_fees}, Starting SOL: {self.starting_sol}")
                logger.error(f"Old balance: {old_balance:.6f}, Deducted: {total_cost:.6f}")
                # SURGICAL: Set to small positive value instead of 0 to prevent division errors
                self.available_sol = 0.001
                logger.error(f"Reset available SOL to: {self.available_sol:.6f}")

            token_amount = (sol_amount / entry_price) if entry_price > 0 else 0.0
            open_time_unix = time.time()
            pos = {
                'token_address': token,
                'entry_price': entry_price,
                'sol_amount': sol_amount,
                'token_amount': token_amount,
                'highest_price': entry_price,
                'open_time': open_time_unix,
                'purchase_time': open_time_unix,  # Add purchase_time for cooldown tracking
                'status': 'OPEN',
                'partial_taken': False,
                'strategy': strategy,
                'position_id': f"{token}-{int(open_time_unix*1000)}",
                'metadata': metadata or {},  # Store additional metadata
                'entry_liquidity_usd': metadata.get('entry_liquidity_usd', 0) if metadata else 0,
                'entry_holder_count': metadata.get('entry_holder_count', 0) if metadata else 0,
                'entry_top10_percent': metadata.get('entry_top10_percent', 0) if metadata else 0,
                'external_fees_paid': total_external_fees  # Track fees paid for accurate PnL
            }
            self.positions[token] = pos
            # Only log position opening, notifications are handled by bot_controller.py
            logger.info(f"Opened position for {token}: {sol_amount:.4f} SOL + {total_external_fees:.4f} SOL fees @ {entry_price:.8f}, Tokens: {token_amount:.4f}")
            logger.info(f"Available SOL after position + fees: {self.available_sol:.4f}")

            # CRITICAL FIX: Save state immediately after opening position
            self.save_state(force=True)
            return pos

    async def close_position(self, token: str, exit_price: float, reason: str, fraction: float = 1.0) -> Optional[Dict[str, Any]]:
        """Close (fully or partially) a position with deadlock-safe locking, update state and PnL."""
        async with self.safe_position_write_lock(token, "close_position"):
            logger.info(f"SELL DEBUG: Attempting to close position for {token} with exit_price={exit_price}, reason={reason}, fraction={fraction}")
            logger.info(f"SELL DEBUG: Current positions: {list(self.positions.keys())}")

            pos = self.positions.get(token)
            if not pos or pos.get('status') != 'OPEN':
                logger.warning(f"Attempted to close non-existent or already closed position: {token}")
                logger.info(f"SELL DEBUG: Position not found or not open. Position data: {pos}")
                return None

            fraction = max(0.0, min(1.0, fraction))
            if fraction <= 0:
                logger.warning(f"Attempted to close {token} with zero fraction. Skipping.")
                return None

            current_token_amount = pos.get('token_amount', 0)
            sold_token_amount = current_token_amount * fraction

            if sold_token_amount <= 0:
                logger.warning(f"Calculated zero tokens to sell for {token} (current={current_token_amount}, frac={fraction}). Skipping.")
                return None

            # OPTIMIZED: Get sell fees using centralized method
            sell_gas_fee = self._calculate_external_fees(0, 'sell')  # Amount doesn't matter for sell gas fee

            # Get percentage fees from cache
            fees = self._external_fees_cache
            handling_fee_percent = fees['handling_fee_percent']
            platform_fee_percent = fees['platform_fee_percent']

            sol_received_gross = sold_token_amount * exit_price

            # Deduct percentage fees from SOL received
            handling_fee_sol = sol_received_gross * (handling_fee_percent / 100.0)
            platform_fee_sol = sol_received_gross * (platform_fee_percent / 100.0)

            # Net SOL received after percentage fees
            sol_received = sol_received_gross - handling_fee_sol - platform_fee_sol

            # Additional gas fee is deducted from available SOL (not from trade proceeds)
            self.available_sol -= sell_gas_fee

            logger.info(f"[MONEY] SELL FEES (SIM/REAL): Gas: {sell_gas_fee:.4f} SOL, Handling: {handling_fee_sol:.4f} SOL, Platform: {platform_fee_sol:.4f} SOL")
            logger.info(f"[MONEY] SOL received: {sol_received_gross:.4f} gross - {handling_fee_sol + platform_fee_sol:.4f} fees = {sol_received:.4f} net")

            initial_sol_amount = pos.get('sol_amount', 0)
            initial_token_amount = initial_sol_amount / pos.get('entry_price', 1) if pos.get('entry_price', 0) > 0 else 0

            if initial_token_amount <= 0:
                cost_basis_per_token = 0
            else:
                # Include external fees paid during buy in cost basis
                external_fees_paid = pos.get('external_fees_paid', 0)
                total_cost_basis = initial_sol_amount + external_fees_paid
                cost_basis_per_token = total_cost_basis / initial_token_amount

            cost_of_sold_tokens = sold_token_amount * cost_basis_per_token
            # PnL = Net SOL received - Cost basis - Sell gas fee
            pnl_sol = sol_received - cost_of_sold_tokens - sell_gas_fee
            pnl_percent = ((exit_price / pos.get('entry_price', 1)) - 1) * 100 if pos.get('entry_price', 0) > 0 else 0

            self.available_sol += sol_received
            is_full_close = abs(1.0 - fraction) < 1e-9 or abs(current_token_amount - sold_token_amount) < 1e-9
            event_type = 'SELL' if is_full_close else 'PARTIAL_SELL'
            pos['token_amount'] -= sold_token_amount

            trade_details = {
                'event_type': event_type,
                'token': token,
                'entry_price': pos.get('entry_price', 0),
                'exit_price': exit_price,
                'sol_cost_basis_traded': cost_of_sold_tokens,
                'token_amount_sold': sold_token_amount,
                'sol_received': sol_received,
                'exit_fraction': fraction,
                'reason': reason,
                'pnl_sol': pnl_sol,
                'pnl_percent': pnl_percent,
                'strategy': pos.get('strategy', 'default'),
                'position_id': pos.get('position_id'),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            self.trade_history.append(trade_details)
            logger.info(f"{event_type} for {token}: Sold {sold_token_amount:.4f} tokens @ {exit_price:.6f}. PnL: {pnl_sol:.4f} SOL")

            if is_full_close:
                pos['status'] = 'CLOSED'
                pos['exit_price'] = exit_price
                pos['exit_time'] = time.time()
                pos['exit_reason'] = reason

                # CRITICAL FIX: Use percentage for win/loss determination (excludes fees)
                entry_price = pos.get('entry_price', exit_price)  # Fallback to exit price if no entry price
                pnl_percent = ((exit_price - entry_price) / entry_price) * 100 if entry_price > 0 else 0
                if pnl_percent > 0:
                    self.win_count += 1
                    self.winning_trades += 1
                    logger.info(f"✅ WIN recorded: {pnl_percent:+.1f}% profit")
                else:
                    self.loss_count += 1
                    logger.info(f"❌ LOSS recorded: {pnl_percent:+.1f}% loss")
                self.total_profit_sol += pnl_sol
                self.total_profit += pnl_sol
                self.trades_count += 1
                self.total_trades += 1

                logger.info(f"Position {token} closed. Total Profit SOL: {self.total_profit_sol:.4f}, Wins: {self.win_count}, Losses: {self.loss_count}")

                # CRITICAL FIX: Remove closed position from positions dict
                logger.info(f"🗑️ REMOVING CLOSED POSITION: {token} from positions dict")
                del self.positions[token]
                logger.info(f"✅ Position {token} removed from active positions")

            else:
                pos['partial_taken'] = True
                self.total_profit_sol += pnl_sol
                logger.info(f"Partial sell for {token}. Remaining tokens: {pos['token_amount']:.4f}")

            self._recalculate_available_sol()

            # SURGICAL FIX: Clear all caches when position closes
            if is_full_close:
                self._clear_position_caches(token)

            # CRITICAL FIX: Force save state immediately after position closure
            self.save_state(force=True)
            logger.info(f"💾 State saved after position closure for {token}")
            return trade_details

    def _clear_position_caches(self, token_address: str):
        """SURGICAL FIX: Clear all monitoring and analysis caches for closed position"""
        try:
            # Import main module to access global caches
            import main

            # Clear monitoring cache
            if hasattr(main, '_monitoring_cache'):
                cache_keys_to_remove = [
                    key for key in main._monitoring_cache.keys()
                    if token_address in key
                ]
                for key in cache_keys_to_remove:
                    del main._monitoring_cache[key]
                    logger.debug(f"Cleared monitoring cache for key: {key}")

            # Clear signal cache
            if hasattr(main, '_signal_cache'):
                cache_keys_to_remove = [
                    key for key in main._signal_cache.keys()
                    if token_address in key
                ]
                for key in cache_keys_to_remove:
                    del main._signal_cache[key]
                    logger.debug(f"Cleared signal cache for key: {key}")

            # Clear API timestamp tracking
            if hasattr(main, '_api_call_timestamps'):
                api_keys_to_remove = [
                    key for key in main._api_call_timestamps.keys()
                    if token_address in key
                ]
                for key in api_keys_to_remove:
                    del main._api_call_timestamps[key]
                    logger.debug(f"Cleared API timestamp for key: {key}")

            logger.info(f"Cleared all caches for closed position: {token_address}")

        except Exception as e:
            logger.warning(f"Error clearing caches for {token_address}: {e}")

    def get_position(self, token: str) -> Optional[Dict[str, Any]]:
        """Get data for a specific position."""
        return self.positions.get(token)

    def get_open_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get all currently open positions."""
        return {tk: data for tk, data in self.positions.items() if data.get('status') == 'OPEN'}

    def is_position_open(self, token: str) -> bool:
        """Check if a position is currently open"""
        pos = self.positions.get(token)
        return pos is not None and pos.get('status') == 'OPEN'

    def get_open_position_tokens(self) -> set:
        """Get set of all tokens with open positions"""
        return {token for token, pos in self.positions.items()
                if pos.get('status') == 'OPEN'}

    async def safe_get_position(self, token: str) -> Optional[Dict[str, Any]]:
        """Safely get position data using read lock"""
        async with self.safe_position_read_lock(token, "get_position"):
            return self.positions.get(token)

    async def safe_update_position_price(self, token: str, price: float, timestamp: float):
        """Safely update position price using read lock (non-destructive update)"""
        async with self.safe_position_read_lock(token, "update_price"):
            pos = self.positions.get(token)
            if pos and pos.get('status') == 'OPEN':
                pos['current_price'] = price
                pos['last_price_update'] = timestamp
                # Also update highest price if this is higher
                pos['highest_price'] = max(pos.get('highest_price', 0), price)
                logger.debug(f"Updated position price for {token}: ${price:.8f} at {timestamp}")

    def add_position(self, token_address: str, position_data: Dict[str, Any]) -> None:
        """
        Add a new position to the state.

        Args:
            token_address: The token address
            position_data: The position data
        """
        logger.info(f"Adding position for {token_address}: {position_data}")
        self.positions[token_address] = position_data

    def update_position(self, token_address: str, position_data: Dict[str, Any]) -> None:
        """
        Update an existing position.

        Args:
            token_address: The token address
            position_data: The updated position data
        """
        logger.info(f"Updating position for {token_address}: {position_data}")
        if token_address in self.positions:
            self.positions[token_address].update(position_data)
        else:
            logger.warning(f"Attempted to update non-existent position: {token_address}")

    def remove_position(self, token_address: str) -> None:
        """
        Remove a position from the state.

        Args:
            token_address: The token address to remove
        """
        logger.info(f"Removing position for {token_address}")
        if token_address in self.positions:
            del self.positions[token_address]
        else:
            logger.warning(f"Attempted to remove non-existent position: {token_address}")

    def has_position(self, token_address: str) -> bool:
        """
        Check if a position exists for the given token address.

        Args:
            token_address: The token address to check

        Returns:
            True if the position exists, False otherwise
        """
        return token_address in self.positions

    def is_position_open(self, token_address: str) -> bool:
        """
        Check if an open position exists for the given token address.

        Args:
            token_address: The token address to check

        Returns:
            True if an open position exists, False otherwise
        """
        pos = self.positions.get(token_address)
        return pos is not None and pos.get('status') == 'OPEN'

    def get_all_positions(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all positions, both open and closed.

        Returns:
            Dictionary of all positions
        """
        return self.positions

    def get_available_sol(self) -> float:
        """
        Get the available SOL balance.

        Returns:
            Available SOL balance
        """
        return self.available_sol

    def clear_state(self) -> None:
        """
        Clear the current state, resetting all positions and trade history.
        """
        logger.info("Clearing state")
        self.positions = {}
        self.trade_history = []
        self.token_last_trade_time = {}
        self.token_cooldowns = {}
        self.agent_limits = {}
        # Moonbags feature has been removed as requested
        self.total_profit_sol = 0.0
        self.total_profit_usd = 0.0
        self.trades_count = 0
        self.win_count = 0
        self.loss_count = 0
        self.max_drawdown = 0.0
        self.current_strategy = 'default'

        # Reset additional attributes for status display
        self.total_trades = 0
        self.winning_trades = 0
        self.total_profit = 0.0

        # Reset available SOL based on starting_sol
        self.available_sol = self.starting_sol

        logger.info(f"State cleared. Available SOL reset to {self.available_sol:.4f}")

        # Save state immediately to ensure persistence
        self.save_state()

    def update_position_highest_price(self, token: str, price: float):
        """Update the highest price seen for an open position."""
        pos = self.positions.get(token)
        if pos and pos.get('status') == 'OPEN':
            pos['highest_price'] = max(pos.get('highest_price', 0), price)

    def update_position_price(self, token: str, price: float, timestamp: float):
        """Update the current price and timestamp for a position (legacy method - use safe_update_position_price for new code)."""
        pos = self.positions.get(token)
        if pos and pos.get('status') == 'OPEN':
            pos['current_price'] = price
            pos['last_price_update'] = timestamp
            # Also update highest price if this is higher
            pos['highest_price'] = max(pos.get('highest_price', 0), price)
            logger.debug(f"Updated position price for {token}: ${price:.8f} at {timestamp}")

    def update_position_status(self, token: str, status: str):
        """Update the status of a position (e.g., OPEN, CLOSED, FAILED)."""
        pos = self.positions.get(token)
        if pos:
            old_status = pos.get('status', 'UNKNOWN')
            pos['status'] = status
            logger.info(f"Updated position status for {token}: {old_status} → {status}")
            # Save state immediately to ensure persistence
            self.save_state()
        else:
            logger.warning(f"Cannot update status for {token}: position not found")

    def get_stats(self) -> Dict[str, Any]:
        """Calculate and return current trading statistics."""
        total_trades = self.win_count + self.loss_count
        winrate = (self.win_count / total_trades * 100) if total_trades > 0 else 0
        roi_percent = (self.total_profit_sol / self.starting_sol * 100) if self.starting_sol > 0 else 0

        return {
            'starting_sol': self.starting_sol,
            'current_available_sol': self.available_sol,
            'total_profit_sol': self.total_profit_sol,
            'roi_percent': roi_percent,
            'total_trades': total_trades,
            'win_count': self.win_count,
            'loss_count': self.loss_count,
            'winrate_percent': winrate,
            'open_positions_count': len(self.get_open_positions())
            # Moonbags feature removed as requested
        }

    def reset_agent_limits(self):
        """Reset all agent limits"""
        self.agent_limits = {}

    def update_agent_limit(self, agent: str, amount: float):
        """Update agent limit after a trade"""
        if agent not in self.agent_limits:
            pass
        else:
            self.agent_limits[agent] -= amount

    def get_agent_limit(self, agent: str) -> float:
        """Get remaining limit for an agent"""
        return self.agent_limits.get(agent, float('inf'))

    def save_state(self, force=False):
        """Save current state to file using atomic write to prevent corruption"""
        current_time = time.time()
        if not force and current_time - self.last_state_save < self.state_save_interval:
            return

        try:
            # Prepare state data
            state_to_save = {
                'positions': self.positions,
                'trade_history': self.trade_history,
                'token_last_trade_time': self.token_last_trade_time,
                'token_cooldowns': self.token_cooldowns,
                'agent_limits': self.agent_limits,
                # Moonbags feature removed as requested
                'total_profit_sol': self.total_profit_sol,
                'total_profit_usd': self.total_profit_usd,
                'trades_count': self.trades_count,
                'win_count': self.win_count,
                'loss_count': self.loss_count,
                'max_drawdown': self.max_drawdown,
                'current_strategy': self.current_strategy,
                'starting_sol': self.starting_sol,
                'available_sol': self.available_sol,
                'sol_per_trade': self.sol_per_trade,  # Save sol_per_trade for persistence
                'save_timestamp': current_time
            }

            # Create a temporary file for atomic write
            temp_file_path = f"{self.state_file_path}.tmp"

            # Write to temporary file first
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                json.dump(state_to_save, f, indent=2)
                # Ensure data is written to disk
                f.flush()
                os.fsync(f.fileno())

            # Create a backup of the current state file if it exists
            if os.path.exists(self.state_file_path):
                backup_path = f"{self.state_file_path}.bak"
                try:
                    shutil.copy2(self.state_file_path, backup_path)
                except Exception as backup_error:
                    logger.warning(f"Failed to create backup before save: {backup_error}")

            # Atomic rename of temporary file to actual state file
            # This ensures we don't end up with a partially written file
            if os.name == 'nt':  # Windows
                # Windows doesn't allow atomic replace, so we need to delete first
                if os.path.exists(self.state_file_path):
                    try:
                        os.remove(self.state_file_path)
                    except PermissionError:
                        logger.warning(f"Cannot remove {self.state_file_path} - file is locked. Using temp file as state.")
                        # If we can't remove the file, use a different name for the state file
                        self.state_file_path = f"{self.state_file_path}.new"
                        # If that file also exists, try to remove it
                        if os.path.exists(self.state_file_path):
                            try:
                                os.remove(self.state_file_path)
                            except:
                                # If we still can't remove it, generate a unique name
                                self.state_file_path = f"{self.state_file_path}.{int(time.time())}"

            try:
                os.rename(temp_file_path, self.state_file_path)
            except Exception as e:
                logger.error(f"Error renaming temp file to state file: {e}")
                # If rename fails, at least keep the temp file
                logger.warning(f"State saved to temporary file: {temp_file_path}")

            self.last_state_save = current_time
            logger.info(f"State saved successfully to {self.state_file_path}")

        except Exception as e:
            logger.error(f"Failed to save state to {self.state_file_path}: {e}", exc_info=True)
            # If we failed, but the temp file exists, try to clean it up
            if os.path.exists(f"{self.state_file_path}.tmp"):
                try:
                    os.remove(f"{self.state_file_path}.tmp")
                except:
                    pass

    def load_state(self):
        """Load state from file, handling potential corruption or compression."""
        # First, check for the main state file
        if not os.path.exists(self.state_file_path):
            logger.info(f"No existing state file found at {self.state_file_path}. Starting with fresh state.")
            # Default state is already set by __init__, so just return
            return

        # Try to load from different possible sources in order of preference
        loaded_successfully = False
        state = None
        error_message = "Unknown error during state loading."

        # List of files to try in order of preference
        files_to_try = [
            (self.state_file_path, "Main state file"),
            (f"{self.state_file_path}.bak", "Backup state file"),
            (f"{self.state_file_path}.tmp", "Temporary state file")
        ]

        # Try each file until one loads successfully
        for file_path, file_desc in files_to_try:
            if not os.path.exists(file_path):
                logger.debug(f"{file_desc} not found at {file_path}, skipping.")
                continue

            logger.info(f"Attempting to load state from {file_desc} at {file_path}...")

            try:
                # Try UTF-8 JSON first
                logger.debug(f"Attempting to load {file_path} as UTF-8 JSON...")
                with open(file_path, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                loaded_successfully = True
                logger.info(f"Successfully loaded state from {file_path} (UTF-8 JSON).")
                break  # Exit the loop if successful

            except (UnicodeDecodeError, json.JSONDecodeError) as e_utf8:
                logger.warning(f"Failed to load {file_desc} as UTF-8 JSON: {e_utf8}. Trying gzip...")
                try:
                    # Try gzipped JSON
                    with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                        state = json.load(f)
                    loaded_successfully = True
                    logger.info(f"Successfully loaded state from {file_path} (Gzipped JSON).")
                    break  # Exit the loop if successful
                except (gzip.BadGzipFile, EOFError, json.JSONDecodeError, FileNotFoundError) as e_gzip:
                    error_message = f"Failed to load {file_desc} as gzipped JSON: {e_gzip}. File appears corrupted."
                    logger.warning(error_message)
                except Exception as e_gzip_other:
                    error_message = f"Unexpected error loading gzipped {file_desc}: {e_gzip_other}"
                    logger.error(error_message, exc_info=True)

            except Exception as e_outer:
                error_message = f"Unexpected error loading {file_desc}: {e_outer}"
                logger.error(error_message, exc_info=True)

            logger.warning(f"Failed to load state from {file_desc}, trying next option if available.")

        # If loading succeeded, populate the state variables
        if loaded_successfully and state is not None:
            try:
                # Validate state data
                required_keys = ['positions', 'trade_history', 'total_profit_sol']
                for key in required_keys:
                    if key not in state:
                        raise ValueError(f"Required key '{key}' missing from state data")

                # Populate state variables
                self.positions = state.get('positions', {})
                self.trade_history = state.get('trade_history', [])
                self.token_last_trade_time = state.get('token_last_trade_time', {})
                self.token_cooldowns = state.get('token_cooldowns', {})
                self.agent_limits = state.get('agent_limits', {})
                # Moonbags feature removed as requested - ignore any moonbags data in old state files
                self.total_profit_sol = state.get('total_profit_sol', 0.0)
                self.total_profit_usd = state.get('total_profit_usd', 0.0)
                self.trades_count = state.get('trades_count', 0)
                self.win_count = state.get('win_count', 0)
                self.loss_count = state.get('loss_count', 0)
                self.max_drawdown = state.get('max_drawdown', 0.0)
                self.current_strategy = state.get('current_strategy', 'default')
                self.starting_sol = state.get('starting_sol', 0.0)
                self.available_sol = state.get('available_sol', 0.0)
                self.sol_per_trade = state.get('sol_per_trade', 0.0)  # Load sol_per_trade from state

                # Set additional attributes for status display
                self.total_trades = self.trades_count
                self.winning_trades = self.win_count
                self.total_profit = self.total_profit_sol

                # Log successful load with timestamp if available
                if 'save_timestamp' in state:
                    save_time = datetime.fromtimestamp(state['save_timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                    logger.info(f"Loaded state data saved at {save_time}")
                else:
                    logger.info("Loaded state data (no timestamp available)")

                logger.debug("State variables populated from loaded data.")

                # Save a clean copy of the state if we loaded from backup
                if loaded_successfully and file_path != self.state_file_path:
                    logger.info(f"Loaded from {file_desc}, saving a clean copy to main state file")
                    self.save_state()

                return # Successful load

            except Exception as e_populate:
                error_message = f"Error populating state variables from loaded data: {e_populate}"
                logger.error(error_message, exc_info=True)
                # Fall through to backup/reset

        # If loading failed or populating failed, backup and reset
        logger.warning(f"State loading failed or file was corrupted. Backing up and starting with fresh state. Reason: {error_message}")
        backup_path = f"{self.state_file_path}.corrupted.{int(time.time())}"
        try:
            if os.path.exists(self.state_file_path):
                shutil.copy2(self.state_file_path, backup_path)
                logger.info(f"Created backup of corrupted state file at {backup_path}")
                # Try to remove the corrupted file to prevent future issues
                try:
                    os.remove(self.state_file_path)
                    logger.info(f"Removed corrupted state file {self.state_file_path}")
                except Exception as remove_error:
                    logger.warning(f"Failed to remove corrupted state file: {remove_error}")
        except Exception as backup_error:
            logger.error(f"Failed to create backup of corrupted state file: {backup_error}")

        # Initialize with default values (ensure they match __init__)
        self.positions = {}
        self.trade_history = []
        self.token_last_trade_time = {}
        self.token_cooldowns = {}
        self.agent_limits = {}
        # Moonbags feature removed as requested
        self.total_profit_sol = 0.0
        self.total_profit_usd = 0.0
        self.trades_count = 0
        self.win_count = 0
        self.loss_count = 0
        self.max_drawdown = 0.0
        self.current_strategy = 'default'
        # Keep starting_sol potentially loaded from config if state load fails
        # Reset available_sol based on starting_sol
        if self.starting_sol <= 0: # If starting_sol wasn't loaded previously from config
             # Use total_sol_capital from trading_settings for real trading
             trading_settings = self.config_manager.get_trading_settings()
             self.starting_sol = trading_settings.get('total_sol_capital', 8.0)  # Use total_sol_capital from config
        self.available_sol = self.starting_sol
        logger.info("Initialized with default state values.")

        # Save a clean initial state
        self.save_state()

    def cleanup(self):
        """Cleanup resources and save final state."""
        logger.info("Saving final state before cleanup...")
        # Force save regardless of time interval
        last_save = self.last_state_save
        self.last_state_save = 0  # Reset to ensure save happens
        self.save_state()
        # Restore last save time if save fails
        if self.last_state_save == 0:
            self.last_state_save = last_save
        logger.info("StateManager cleanup finished.")

    def reset_capital(self, new_starting_sol: float):
        """Resets the capital and related stats for a new session.
           Keeps trade history and open positions.
        """
        if new_starting_sol <= 0:
             logger.error(f"Invalid new starting SOL amount: {new_starting_sol}. Capital not reset.")
             return False

        logger.info(f"Resetting capital. Old starting SOL: {self.starting_sol:.4f}, New starting SOL: {new_starting_sol:.4f}")
        self.starting_sol = new_starting_sol
        # Reset profit/loss stats
        self.total_profit_sol = 0.0
        self.total_profit = 0.0
        self.win_count = 0
        self.winning_trades = 0
        self.loss_count = 0
        self.trades_count = 0
        self.total_trades = 0
        # Recalculate available SOL based on new start and existing open positions
        self._recalculate_available_sol()
        logger.info(f"Capital reset complete. New Available SOL: {self.available_sol:.4f}")
        self.save_state() # Save the new capital state
        return True

    def reset_agent_limits(self):
        """Reset all agent limits"""
        self.agent_limits = {}

    def force_state_sync_and_cleanup(self):
        """
        CRITICAL FIX: Force state synchronization and cleanup duplicate positions.
        This fixes the balance discrepancy and position tracking issues.
        """
        logger.info("🔧 FORCE STATE SYNC AND CLEANUP STARTING...")

        # Step 1: Check for duplicate positions (same token multiple times)
        duplicate_tokens = {}
        total_duplicates_found = 0

        for token_address, position_data in list(self.positions.items()):
            if token_address in duplicate_tokens:
                total_duplicates_found += 1
                logger.warning(f"DUPLICATE POSITION FOUND: {token_address} (duplicate #{total_duplicates_found})")

                # Merge duplicate positions (combine SOL amounts)
                original_position = duplicate_tokens[token_address]
                duplicate_position = position_data

                # Combine SOL amounts
                original_position['sol_amount'] += duplicate_position['sol_amount']
                original_position['token_amount'] += duplicate_position['token_amount']

                # Use the latest entry price (most recent duplicate)
                original_position['entry_price'] = duplicate_position['entry_price']
                original_position['highest_price'] = max(
                    original_position.get('highest_price', 0),
                    duplicate_position.get('highest_price', 0)
                )

                # Remove the duplicate
                del self.positions[token_address]
                logger.info(f"MERGED DUPLICATE: Combined SOL amount now {original_position['sol_amount']:.4f}")

            else:
                duplicate_tokens[token_address] = position_data

        # Step 2: Recalculate available SOL based on actual open positions
        total_sol_in_positions = 0.0
        open_positions_count = 0

        for token_address, position_data in self.positions.items():
            if position_data.get('status') == 'OPEN':
                sol_amount = position_data.get('sol_amount', 0.0)
                total_sol_in_positions += sol_amount
                open_positions_count += 1
                logger.info(f"OPEN POSITION: {token_address[:8]}... = {sol_amount:.4f} SOL")

        # Step 3: Fix available SOL calculation
        correct_available_sol = self.starting_sol - total_sol_in_positions
        old_available_sol = self.available_sol
        self.available_sol = max(0.0, correct_available_sol)  # Ensure non-negative

        logger.info(f"SOL BALANCE CORRECTION:")
        logger.info(f"  Starting SOL: {self.starting_sol:.4f}")
        logger.info(f"  SOL in {open_positions_count} positions: {total_sol_in_positions:.4f}")
        logger.info(f"  Old available SOL: {old_available_sol:.4f}")
        logger.info(f"  Corrected available SOL: {self.available_sol:.4f}")
        logger.info(f"  Balance difference: {self.available_sol - old_available_sol:.4f}")

        # Step 4: Fix trade statistics (only count closed positions as trades)
        closed_trades = 0
        wins = 0
        losses = 0
        total_realized_profit = 0.0

        for position_data in self.positions.values():
            if position_data.get('status') == 'CLOSED':
                closed_trades += 1
                pnl = position_data.get('pnl_sol', 0.0)
                total_realized_profit += pnl
                if pnl > 0:
                    wins += 1
                else:
                    losses += 1

        # Update statistics to reflect only closed trades
        old_stats = {
            'trades_count': self.trades_count,
            'win_count': self.win_count,
            'loss_count': self.loss_count,
            'total_profit_sol': self.total_profit_sol
        }

        self.trades_count = closed_trades
        self.total_trades = closed_trades
        self.win_count = wins
        self.winning_trades = wins
        self.loss_count = losses
        self.total_profit_sol = total_realized_profit
        self.total_profit = total_realized_profit

        logger.info(f"STATISTICS CORRECTION:")
        logger.info(f"  Old: {old_stats['trades_count']} trades, {old_stats['win_count']} wins, {old_stats['total_profit_sol']:.4f} SOL profit")
        logger.info(f"  New: {self.trades_count} trades, {self.win_count} wins, {self.total_profit_sol:.4f} SOL profit")
        logger.info(f"  Open positions (not counted): {open_positions_count}")

        # Step 5: Force save the corrected state
        self.last_state_save = 0  # Force immediate save
        self.save_state()

        logger.info(f"🎉 STATE SYNC COMPLETE:")
        logger.info(f"  ✅ Removed {total_duplicates_found} duplicate positions")
        logger.info(f"  ✅ Corrected available SOL balance")
        logger.info(f"  ✅ Fixed trade statistics")
        logger.info(f"  ✅ {open_positions_count} open positions properly tracked")
        logger.info(f"  ✅ State saved to {self.state_file_path}")

        return {
            'duplicates_removed': total_duplicates_found,
            'open_positions': open_positions_count,
            'available_sol': self.available_sol,
            'total_trades': self.trades_count,
            'win_rate': (self.win_count / self.trades_count * 100) if self.trades_count > 0 else 0
        }

    def fix_corrupted_state(self):
        """Fix corrupted state file by creating a fresh state file"""
        logger.info("Attempting to fix corrupted state file...")

        # Backup any existing state files
        for file_suffix in ['', '.bak', '.tmp']:
            file_path = f"{self.state_file_path}{file_suffix}"
            if os.path.exists(file_path):
                backup_path = f"{file_path}.backup.{int(time.time())}"
                try:
                    shutil.copy2(file_path, backup_path)
                    logger.info(f"Backed up {file_path} to {backup_path}")
                    # Remove the original file
                    os.remove(file_path)
                    logger.info(f"Removed {file_path}")
                except Exception as e:
                    logger.error(f"Error backing up {file_path}: {e}")

        # Clear all state in memory
        self.clear_state()

        # Log the action
        logger.info("State cleared and reset to defaults")

        # Return success
        return True