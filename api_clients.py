import asyncio
import aiohttp
import logging
import time
import re
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime

from config_manager import ConfigManager
from dotenv import load_dotenv
from error_handling import robust_api_call, safe_api_call, get_circuit_breaker, get_timeout
# Hybrid API client removed

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class RateLimiter:
    """Handles API rate limiting and caching with optimized timing"""

    def __init__(self):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.stats: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger('rate_limiter')

        # Track last request time per provider
        self.last_request_time: Dict[str, float] = {}

        # Track last request time per token (for token-specific rate limiting)
        self.token_last_request_time: Dict[str, Dict[str, float]] = {
            'dexscreener': {}
        }

        # FIXED: Updated to use exact DexScreener 285 RPM limit
        self.rate_limits: Dict[str, float] = {
            'dexscreener': 0.211,  # 4.75 requests per second (285 RPM - exact limit)
            'default': 1.0         # Default rate limit
        }

        # Token-specific rate limits (seconds between requests for same token)
        self.token_rate_limits: Dict[str, float] = {
            'dexscreener': 15.0,  # 1 req per token per 15 seconds unless active position
            'active_position': 3.0 # For tokens with active positions, check more frequently
        }

    async def wait(self, provider: str, token_address: str = None, is_active_position: bool = False) -> None:
        """
        Wait for rate limit to expire before making a request.
        Implements both global API rate limiting and token-specific rate limiting.

        Args:
            provider: The API provider name (e.g., 'dexscreener', 'moralis')
            token_address: Optional token address for token-specific rate limiting
            is_active_position: Whether this token has an active position (affects rate limit)
        """
        current_time = time.time()

        # 1. Apply global rate limiting for the provider
        rate_limit = self.rate_limits.get(provider, self.rate_limits['default'])
        last_time = self.last_request_time.get(provider, 0)
        time_since_last_request = current_time - last_time

        if time_since_last_request < rate_limit:
            wait_time = rate_limit - time_since_last_request
            self.logger.debug(f"Global rate limiting {provider} API: waiting {wait_time:.2f}s")
            await asyncio.sleep(wait_time)

        # Update the global last request time
        self.last_request_time[provider] = time.time()

        # 2. Apply token-specific rate limiting if a token address is provided
        if token_address and provider in self.token_last_request_time:
            # Get the appropriate rate limit based on whether this is an active position
            if is_active_position:
                token_rate_limit = self.token_rate_limits.get('active_position', 3.0)
            else:
                token_rate_limit = self.token_rate_limits.get(provider, 15.0)

            # Get the last request time for this specific token
            token_last_time = self.token_last_request_time[provider].get(token_address, 0)
            token_time_since_last = current_time - token_last_time

            if token_time_since_last < token_rate_limit:
                token_wait_time = token_rate_limit - token_time_since_last
                self.logger.debug(f"Token-specific rate limiting for {token_address} ({provider}): waiting {token_wait_time:.2f}s")
                await asyncio.sleep(token_wait_time)

            # Update the token-specific last request time
            self.token_last_request_time[provider][token_address] = time.time()

    def clear_cache(self, older_than_seconds: float = None) -> int:
        """Clear cached items older than specified time"""
        now = datetime.now()
        count = 0

        for key in list(self.cache.keys()):
            item = self.cache[key]
            if older_than_seconds is None or (now - item['timestamp']).total_seconds() > older_than_seconds:
                del self.cache[key]
                count += 1

        # Also clear last request times if older_than_seconds is None (full clear)
        if older_than_seconds is None:
            self.last_request_time.clear()

        return count

    def reset_stats(self):
        """Reset API statistics"""
        self.stats = {}

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get API usage statistics"""
        return self.stats



    async def make_api_request(
        self,
        session: aiohttp.ClientSession,
        url: str,
        provider: str,
        cache_key: str,
        token_address: str = None,
        is_active_position: bool = False,
        headers: Dict[str, str] = None,
        max_retries: int = 2,  # PERMANENT FIX: Faster retries
        retry_delay: float = 0.3,  # PERMANENT FIX: Faster retry delay
        timeout: float = 1.0,  # CRITICAL FIX: Ultra-fast timeout for meme coins
        method: str = "GET",
        data: Dict[str, Any] = None,
        cache_duration: int = 30
    ) -> Any:
        """Make an API request with optimized rate limiting, caching, and robust retries."""
        # Check cache first
        if cache_key in self.cache:
            item = self.cache[cache_key]
            cache_age = (datetime.now() - item['timestamp']).total_seconds()

            # For active positions in meme trading, use ZERO cache for real-time data
            # Meme coins can move 50%+ in 1 second - we need fresh data every time
            if is_active_position:
                effective_cache_duration = 0  # NO CACHE for active positions - always fresh data

                # For extremely volatile tokens (those with recent price changes > 5%),
                # bypass cache completely for active positions
                if item.get('volatility_flag') and cache_age > 1:
                    self.logger.debug(f"Cache bypass for volatile active position {token_address}")
                    # Don't return cached data, continue to API call
                else:
                    if cache_age < effective_cache_duration:
                        self.logger.debug(f"Active position cache hit for {provider} ({cache_key}), age: {cache_age:.1f}s")
                        return item['data']
                    else:
                        self.logger.debug(f"Active position cache expired for {provider} ({cache_key}), age: {cache_age:.1f}s")
            else:
                # For non-active positions, use standard cache duration
                if cache_age < cache_duration:
                    self.logger.debug(f"Cache hit for {provider} ({cache_key}), age: {cache_age:.1f}s")
                    return item['data']
                else:
                    self.logger.debug(f"Cache expired for {provider} ({cache_key}), age: {cache_age:.1f}s")

        # Initialize stats for provider if needed
        if provider not in self.stats:
            self.stats[provider] = {
                'requests': 0,
                'successes': 0,
                'failures': 0,
                'response_times': [],
                'avg_response_time': 0.0
            }

        # Apply rate limiting with token-specific controls
        await self.wait(provider, token_address, is_active_position)

        # Record start time for performance tracking
        request_start_time = time.time() # Renamed to avoid conflict with response_time

        # Make the request with retries and exponential backoff
        for attempt in range(max_retries + 1):
            try:
                self.logger.debug(f"Attempt {attempt + 1}/{max_retries + 1} for {provider} API request to {url}")
                # PERMANENT FIX: Properly use asyncio.wait_for with session.request
                response = await asyncio.wait_for(
                    session.request(method, url, headers=headers, json=data),
                    timeout=timeout
                )
                async with response:
                    # Track request in stats
                    self.stats[provider]['requests'] += 1

                    # Calculate response time for this attempt
                    current_response_time = time.time() - request_start_time # Use request_start_time
                    self.stats[provider]['response_times'].append(current_response_time)

                    # Keep only the last 100 response times
                    if len(self.stats[provider]['response_times']) > 100:
                        self.stats[provider]['response_times'] = self.stats[provider]['response_times'][-100:]

                    # Update average response time
                    self.stats[provider]['avg_response_time'] = sum(self.stats[provider]['response_times']) / len(self.stats[provider]['response_times'])

                    if response.status == 200:
                        try:
                            response_data = await response.json()
                            self.stats[provider]['successes'] += 1

                            # Cache successful response
                            self.cache[cache_key] = {
                                'data': response_data,
                                'timestamp': datetime.now(),
                                'volatility_flag': False # Default, can be updated by caller if needed
                            }
                            self.logger.info(f"API request to {provider} successful in {current_response_time:.3f}s (Attempt {attempt + 1})")
                            return response_data
                        except Exception as json_error:
                            self.stats[provider]['failures'] += 1
                            self.logger.error(f"API response JSON parsing error from {provider} for {url} (Attempt {attempt + 1}): {json_error}")
                            # Do not retry on JSON parsing error, it's a server-side or data issue
                            return None
                    else:
                        self.stats[provider]['failures'] += 1
                        error_message = f"API request to {provider} for {url} failed (Attempt {attempt + 1}/{max_retries + 1}): {response.status} {response.reason}"
                        self.logger.error(error_message)

                        # For rate limiting (429) or server errors (5xx), wait and retry
                        if response.status == 429 or response.status >= 500:
                            if attempt < max_retries:
                                # Exponential backoff
                                current_retry_delay = retry_delay * (2 ** attempt)
                                # Respect Retry-After header if present for 429
                                if response.status == 429:
                                    try:
                                        retry_after_header = int(response.headers.get('Retry-After', current_retry_delay))
                                        current_retry_delay = max(current_retry_delay, retry_after_header) # Use the longer delay
                                    except ValueError:
                                        self.logger.warning(f"Invalid Retry-After header from {provider}: {response.headers.get('Retry-After')}")

                                self.logger.warning(f"Retrying {provider} request to {url} in {current_retry_delay:.2f}s due to {response.status}...")
                                await asyncio.sleep(current_retry_delay)
                                request_start_time = time.time() # Reset start time for the new attempt
                                continue # Go to next attempt
                            else:
                                self.logger.error(f"Max retries reached for {provider} request to {url} after {response.status} error.")
                                return None # All retries failed for 429 or 5xx
                        else:
                            # For other client errors (4xx, excluding 429), do not retry
                            self.logger.error(f"Not retrying {provider} request to {url} due to client error: {response.status}")
                            return None

            except asyncio.TimeoutError:
                self.stats[provider]['failures'] += 1
                self.logger.error(f"API request to {provider} for {url} timed out after {timeout}s (Attempt {attempt + 1}/{max_retries + 1})")
                if attempt < max_retries:
                    current_retry_delay = retry_delay * (2 ** attempt)
                    self.logger.warning(f"Retrying {provider} request to {url} in {current_retry_delay:.2f}s due to timeout...")
                    await asyncio.sleep(current_retry_delay)
                    request_start_time = time.time() # Reset start time for the new attempt
                    continue # Go to next attempt
                else:
                    self.logger.error(f"Max retries reached for {provider} request to {url} after timeout.")
                    return None # All retries failed due to timeout

            except aiohttp.ClientConnectorError as e:
                self.stats[provider]['failures'] += 1
                self.logger.error(f"ClientConnectorError for {provider} API request to {url} (Attempt {attempt + 1}): {e}")
                if attempt < max_retries:
                    current_retry_delay = retry_delay * (2 ** attempt)
                    self.logger.warning(f"Retrying {provider} request to {url} in {current_retry_delay:.2f}s due to ClientConnectorError...")
                    await asyncio.sleep(current_retry_delay)
                    request_start_time = time.time() # Reset start time for the new attempt
                    continue
                else:
                    self.logger.error(f"Max retries reached for {provider} request to {url} after ClientConnectorError.")
                    return None
            except Exception as e:
                self.stats[provider]['failures'] += 1
                self.logger.error(f"Unexpected error during API request to {provider} for {url} (Attempt {attempt + 1}): {e}")
                logger.error(traceback.format_exc()) # Log full traceback for unexpected errors
                if attempt < max_retries:
                    current_retry_delay = retry_delay * (2 ** attempt)
                    self.logger.warning(f"Retrying {provider} request to {url} in {current_retry_delay:.2f}s due to unexpected error...")
                    await asyncio.sleep(current_retry_delay)
                    request_start_time = time.time() # Reset start time for the new attempt
                    continue
                else:
                    self.logger.error(f"Max retries reached for {provider} request to {url} after unexpected error.")
                    return None # All retries failed

        self.logger.error(f"All attempts failed for {provider} API request to {url}")
        return None # Should be unreachable if loop logic is correct, but as a fallback

class TokenAnalyzer:
    """Analyzes tokens using multiple data sources with optimized timing"""

    SOL_TOKEN_ADDRESS = "So11111111111111111111111111111111111111112"

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.rate_limiter = RateLimiter()
        self.session: Optional[aiohttp.ClientSession] = None
        self.sessions: Dict[str, aiohttp.ClientSession] = {}
        self.logger = logging.getLogger('token_analyzer')
        self.price_data = {}  # token -> price
        self.last_price_update = {}  # token -> timestamp
        self.watchlist_tokens = set()
        self.active_positions = set()  # Track tokens with active positions for optimized polling

        # Fetch sections safely
        self.api_timeouts = self.config_manager.get_section('api_timeouts')
        self.api_retries = self.config_manager.get_section('api_retries')
        self.api_retry_delays = self.config_manager.get_section('api_retry_delays')

        # Add price and volume history tracking
        self.price_history = {}  # token -> list of prices
        self.volume_history = {}  # token -> list of volumes
        self.analysis_cache = {}  # token -> analysis result
        self.max_history_length = 100  # Maximum number of historical data points to keep

        # Ensure defaults are applied if sections or keys are missing
        self._apply_api_defaults()

        # FIXED: Load trading settings from centralized rug_protection section
        trading_settings = self.config_manager.get_section('trading_settings')
        rug_protection = trading_settings.get('rug_protection', {})

        self.min_liquidity_sol = self.config_manager.get('trading_settings', 'min_liquidity_sol', default=5.0)
        self.min_vol_5m_usd = rug_protection.get('min_volume_5m_usd', 10000.0)
        self.min_pair_age_mins = rug_protection.get('min_token_age_minutes', 0)
        self.max_pair_age_mins = float('inf')  # Disabled - volume/liquidity/mc/txn activity matters more than age
        self.sol_usd_price: Optional[float] = None
        self.sol_price_last_update: float = 0

        # Track API health status
        self.api_health: Dict[str, str] = {
            'dexscreener': 'Unknown',
            'moralis': 'Unknown'
        }

        # Moralis client removed - using DexScreener only
        self.moralis_client = None

        # Hybrid API client removed
        self.hybrid_client = None

    def _apply_api_defaults(self):
        """Apply default API configurations if not set in config"""
        # Configuration is now handled directly in make_api_request method
        self.logger.info("API configurations applied via make_api_request defaults")

    async def init_sessions(self):
        """Initialize API sessions with optimized connection settings"""
        if not self.session or self.session.closed:
            logger.debug("Initializing new aiohttp session for TokenAnalyzer.")
            # Use a single session for all API calls
            self.session = aiohttp.ClientSession()

            # Initialize sessions dictionary with the session for DexScreener
            self.sessions['dexscreener'] = self.session

            # Initialize Moralis client session if available
            if self.moralis_client:
                try:
                    await self.moralis_client.init_session()
                    logger.info("Initialized Moralis client session")
                except Exception as e:
                    logger.error(f"Error initializing Moralis client session: {e}")

            # Test API connections
            await self._test_api_connections()
        else:
            logger.debug("Using existing aiohttp session for TokenAnalyzer.")

    async def _test_api_connections(self):
        """Test API connections and update health status"""
        # Test DexScreener with increased timeout
        try:
            # Test with SOL token
            dex_data = await self.get_dex_screener_data(self.SOL_TOKEN_ADDRESS)
            if dex_data and dex_data.get('exists', False):
                self.api_health['dexscreener'] = 'Healthy'

                # Log minimal information at debug level
                if 'name' in dex_data and 'symbol' in dex_data:
                    logger.debug(f"DexScreener data for {self.SOL_TOKEN_ADDRESS}: Price=${dex_data.get('price', 0):.2f}, Liquidity=${dex_data.get('liquidity', 0):,.2f}")

                # Only log at info level that the test was successful
                logger.info("DexScreener API connection test successful")
            else:
                self.api_health['dexscreener'] = 'Degraded'
                logger.warning("DexScreener API connection test returned no data")

                # Log the error details if available
                if dex_data and 'error' in dex_data:
                    logger.warning(f"DexScreener error: {dex_data.get('error')}")
        except asyncio.TimeoutError:
            self.api_health['dexscreener'] = 'Degraded'
            logger.warning(f"DexScreener API connection test timed out")
        except Exception as e:
            self.api_health['dexscreener'] = 'Unhealthy'
            logger.error(f"DexScreener API connection test failed: {e}")

        # Moralis API removed
        self.api_health['moralis'] = 'Removed'

    async def close_sessions(self):
        """Close API sessions"""
        # Close the main session if it exists
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

        # Clear the sessions dictionary
        self.sessions.clear()

        # Moralis client removed

        # Hybrid API client removed

    @robust_api_call(max_retries=3, timeout=5.0, fallback_value={"exists": False, "error": "API call failed"})
    async def get_dex_screener_data(self, mint_address: str, is_active_position: bool = False) -> dict:
        """
        Get token data from DexScreener API with comprehensive error handling.
        """
        # Use DexScreener API directly

        # Use the latest DexScreener search endpoint, allowing it to be overridden by config
        dexscreener_search_endpoint_template = self.config_manager.get(
            'api_endpoints',
            'dexscreener', # Use the correct config key
            default="https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}"
        )

        # Construct the URL by replacing the placeholder with the actual mint_address
        url = dexscreener_search_endpoint_template.replace("{chainId}", "solana").replace("{tokenAddress}", mint_address)
        logger.debug(f"Using DexScreener API URL: {url}")

        api_key = self.config_manager.get('api_keys', 'dexscreener_key', default=None)
        headers = {}
        if api_key:
            headers['X-API-KEY'] = api_key # DexScreener v2 uses X-API-KEY
            logger.debug("Using DexScreener API key")

        if not self.session or self.session.closed:
            await self.init_sessions() # Ensure session is initialized via TokenAnalyzer

        cache_key = f"dexscreener_search_{mint_address}"

        # Log before making the call
        logger.debug(f"Fetching DexScreener data for {mint_address} via make_api_request.")

        # Use the robust make_api_request from RateLimiter
        raw_response_data = await self.rate_limiter.make_api_request(
            session=self.session, # Pass the TokenAnalyzer's session
            url=url,
            provider="dexscreener",
            cache_key=cache_key,
            token_address=mint_address,
            is_active_position=is_active_position,
            headers=headers,
            method="GET"
        )

        if raw_response_data is None:
            logger.warning(f"Failed to get data from DexScreener for {mint_address} after all retries or due to non-retryable error.")
            return {"exists": False, "error": "DexScreener API request failed after retries", "should_fallback": True}

        # Log the raw response for debugging if needed (be mindful of log size)
        # logger.debug(f"DexScreener raw response for {mint_address}: {json.dumps(raw_response_data)[:1000]}...")

        try:
            # Handle different response formats from DexScreener API
            pairs = []

            # Handle case where response is a direct array
            if isinstance(raw_response_data, list):
                pairs = raw_response_data
                logger.debug(f"Received DexScreener data as list with {len(pairs)} pairs for {mint_address}")
            # Handle case where response is an object with 'pairs' property
            elif isinstance(raw_response_data, dict) and 'pairs' in raw_response_data:
                pairs = raw_response_data.get('pairs', [])
                logger.debug(f"Received DexScreener data with 'pairs' property containing {len(pairs)} pairs for {mint_address}")
            else:
                logger.warning(f"Unexpected DexScreener response format for {mint_address}: {type(raw_response_data)}")

            # FIX 3: Retry empty responses once before failing
            if not pairs:
                logger.warning(f"No pairs found in DexScreener data for {mint_address} - will retry once")

                # Wait 1 second and retry once for empty responses
                await asyncio.sleep(1)
                retry_response = await self.rate_limiter.make_api_request(
                    session=self.session,
                    url=url,
                    provider="dexscreener",
                    cache_key=f"dexscreener_retry_{mint_address}",
                    token_address=mint_address,
                    is_active_position=is_active_position,
                    headers=headers,
                    method="GET"
                )

                if retry_response:
                    # Process retry response
                    if isinstance(retry_response, list):
                        pairs = retry_response
                    elif isinstance(retry_response, dict) and 'pairs' in retry_response:
                        pairs = retry_response.get('pairs', [])

                    if pairs:
                        logger.info(f"Retry successful for {mint_address} - found {len(pairs)} pairs")
                    else:
                        logger.warning(f"Retry also returned no pairs for {mint_address}")
                        return {"exists": False, "error": "No pairs found after retry"}
                else:
                    logger.warning(f"Retry failed for {mint_address}")
                    return {"exists": False, "error": "No pairs found and retry failed"}

            # FIX 1: Relax pair filtering - check for valid data instead of strict chainId
            # First try to find Solana pairs, but if none exist, use any pair with valid data
            solana_pairs = [p for p in pairs if p.get('chainId', '').lower() == 'solana']

            if not solana_pairs:
                # Check if any pair has valid price and liquidity data regardless of chainId
                valid_pairs = [p for p in pairs if p.get('priceUsd') and float(p.get('priceUsd', 0)) > 0]
                if valid_pairs:
                    logger.info(f"No explicit Solana pairs found for {mint_address}, but found {len(valid_pairs)} pairs with valid data")
                    solana_pairs = valid_pairs
                else:
                    logger.warning(f"No valid pairs found for {mint_address} in response. Raw pairs: {len(pairs)}")
                    return {"exists": False, "error": "No valid pairs found in response"}

            logger.debug(f"Found {len(solana_pairs)} Solana pairs for {mint_address} from DexScreener.")

            # CRITICAL FIX: Handle pump.fun tokens that don't have liquidity data
            # Check if this is a pump.fun token
            is_pumpfun_token = any(p.get('dexId', '').lower() == 'pumpfun' for p in solana_pairs)

            if is_pumpfun_token:
                # For pump.fun tokens, select based on volume instead of liquidity
                logger.info(f"Detected pump.fun token {mint_address}, using volume-based selection")
                sorted_pairs = sorted(
                    solana_pairs,
                    key=lambda p: float(p.get('volume', {}).get('h24', 0) or 0),
                    reverse=True
                )
            else:
                # For regular tokens, select based on liquidity
                sorted_pairs = sorted(
                    solana_pairs,
                    key=lambda p: float(p.get('liquidity', {}).get('usd', 0) or 0),
                    reverse=True
                )

            pair_to_use = sorted_pairs[0]
            logger.debug(f"Selected most liquid pair ({pair_to_use.get('pairAddress')}) for {mint_address}.")

            name = pair_to_use.get('baseToken', {}).get('name', 'Unknown')
            symbol = pair_to_use.get('baseToken', {}).get('symbol', 'Unknown')
            price_usd_str = pair_to_use.get('priceUsd', '0')
            price = float(price_usd_str) if price_usd_str else 0.0

            # FIX 5: Validate data quality - ensure we have valid price
            if price <= 0:
                logger.warning(f"Invalid price data for {mint_address}: price={price}")
                return {"exists": False, "error": "Invalid price data - price is zero or negative"}

            price_change_5m = float(pair_to_use.get('priceChange', {}).get('m5', 0) or 0)
            price_change_1h = float(pair_to_use.get('priceChange', {}).get('h1', 0) or 0)
            price_change_6h = float(pair_to_use.get('priceChange', {}).get('h6', 0) or 0)
            price_change_24h = float(pair_to_use.get('priceChange', {}).get('h24', 0) or 0)

            volume_h24 = float(pair_to_use.get('volume', {}).get('h24', 0) or 0)
            volume_h6 = float(pair_to_use.get('volume', {}).get('h6', 0) or 0)
            volume_h1 = float(pair_to_use.get('volume', {}).get('h1', 0) or 0)
            volume_m5 = float(pair_to_use.get('volume', {}).get('m5', 0) or 0)

            # CRITICAL FIX: Enhanced liquidity extraction with pump.fun token handling
            liquidity_usd = 0
            dex_id = pair_to_use.get('dexId', '').lower()

            if dex_id == 'pumpfun':
                # PUMP.FUN TOKENS: Don't have traditional liquidity - use FDV as proxy
                fdv_value = float(pair_to_use.get('fdv', 0) or 0)
                if fdv_value > 0:
                    liquidity_usd = fdv_value
                    logger.info(f"🎯 PUMP.FUN TOKEN {mint_address}: Using FDV (${liquidity_usd:,.2f}) as liquidity proxy")
                else:
                    # If no FDV, use market cap as fallback
                    market_cap_value = float(pair_to_use.get('marketCap', 0) or 0)
                    if market_cap_value > 0:
                        liquidity_usd = market_cap_value
                        logger.info(f"🎯 PUMP.FUN TOKEN {mint_address}: Using Market Cap (${liquidity_usd:,.2f}) as liquidity proxy")
            else:
                # REGULAR TOKENS: Try multiple possible liquidity field paths
                liquidity_paths = [
                    ['liquidity', 'usd'],
                    ['liquidity', 'base'],
                    ['liquidity'],
                    ['liquidityUsd'],
                    ['baseToken', 'liquidity'],
                    ['quoteToken', 'liquidity']
                ]

                for path in liquidity_paths:
                    try:
                        value = pair_to_use
                        for key in path:
                            if isinstance(value, dict) and key in value:
                                value = value[key]
                            else:
                                value = None
                                break

                        if value is not None:
                            liquidity_usd = float(value)
                            if liquidity_usd > 0:
                                logger.debug(f"Found liquidity via path {' -> '.join(path)}: ${liquidity_usd:,.2f}")
                                break
                    except (ValueError, TypeError, KeyError):
                        continue

            # If still 0, try parsing from string fields that might contain liquidity info
            if liquidity_usd == 0:
                for field in ['info', 'description', 'liquidityString']:
                    if field in pair_to_use:
                        try:
                            # Look for patterns like "$41,774.32" or "41.77K"
                            import re
                            text = str(pair_to_use[field])
                            # Match currency patterns
                            matches = re.findall(r'\$?([0-9,]+\.?[0-9]*)[KMB]?', text)
                            for match in matches:
                                clean_match = match.replace(',', '')
                                if '.' in clean_match:
                                    potential_liq = float(clean_match)
                                    if potential_liq > 1000:  # Reasonable liquidity threshold
                                        liquidity_usd = potential_liq
                                        logger.info(f"Extracted liquidity from {field}: ${liquidity_usd:,.2f}")
                                        break
                        except (ValueError, TypeError):
                            continue
                        if liquidity_usd > 0:
                            break

            logger.debug(f"Final liquidity_usd for {mint_address}: ${liquidity_usd:,.2f}")

            # FIX 5: Validate liquidity quality - but be more lenient for pump.fun tokens
            if liquidity_usd <= 0:
                if dex_id == 'pumpfun':
                    # For pump.fun tokens, try to use market cap as liquidity proxy
                    market_cap_fallback = float(pair_to_use.get('marketCap', 0) or 0)
                    if market_cap_fallback > 0:
                        liquidity_usd = market_cap_fallback
                        logger.info(f"Pump.fun token {mint_address}: Using market cap (${liquidity_usd:,.2f}) as liquidity fallback")
                    else:
                        logger.warning(f"Pump.fun token {mint_address} has no liquidity or market cap data")
                        return {"exists": False, "error": "Pump.fun token has no liquidity or market cap data"}
                else:
                    logger.warning(f"Invalid liquidity data for {mint_address}: liquidity_usd={liquidity_usd}")
                    return {"exists": False, "error": "Invalid liquidity data - liquidity is zero or negative"}

            # market_cap = float(pair_to_use.get('marketCap', 0) or 0) # marketCap is often on token level, not pair
            fdv = float(pair_to_use.get('fdv', 0) or 0) # FDV is usually for the base token

            # Pair creation time and age
            pair_created_at_ms = pair_to_use.get('pairCreatedAt', 0) # DexScreener provides timestamp in ms
            pair_age_minutes = 0
            if pair_created_at_ms and isinstance(pair_created_at_ms, (int, float)) and pair_created_at_ms > 0:
                current_time_s = time.time()
                pair_created_at_s = pair_created_at_ms / 1000
                pair_age_minutes = (current_time_s - pair_created_at_s) / 60

            # Transactions
            txns_h24 = pair_to_use.get('txns', {}).get('h24', {'buys': 0, 'sells': 0})
            txns_h1 = pair_to_use.get('txns', {}).get('h1', {'buys': 0, 'sells': 0})
            txns_m5 = pair_to_use.get('txns', {}).get('m5', {'buys': 0, 'sells': 0})

            # Market cap might be available directly on baseToken if enriched by DexScreener search
            # If not, it might require a separate call to a token-specific endpoint or be derived.
            # For now, use FDV if marketCap isn't directly on the pair. Or look in baseToken from pair data.
            market_cap = float(pair_to_use.get('baseToken',{}).get('marketCap', fdv)) # Use baseToken's marketCap, fallback to pair's FDV as approximation

            logger.debug(f"Processed DexScreener data for {mint_address} ({symbol}): Price=${price:.6f}, Liq=${liquidity_usd:,.0f}, FDV=${fdv:,.0f}, Age={pair_age_minutes:.1f}m")

            return {
                "exists": True,
                "name": name,
                "symbol": symbol,
                "address": mint_address, # The address we queried for
                "pair_address": pair_to_use.get('pairAddress'),
                "price": price,
                "price_native": float(pair_to_use.get('priceNative', '0') or '0'), # Price in native quote currency
                "price_change_5m": price_change_5m,
                "price_change_1h": price_change_1h,
                "price_change_6h": price_change_6h,
                "price_change_24h": price_change_24h,
                "volume_24h": volume_h24,
                "volume_6h": volume_h6,
                "volume_1h": volume_h1,
                "volume_5m": volume_m5,
                "liquidity": liquidity_usd, # Ensure this key matches what analyze() expects
                "market_cap": market_cap,
                "fdv": fdv,
                "pair_age_minutes": pair_age_minutes, # Ensure this key matches
                "pairCreatedAt": pair_created_at_ms,
                "txns": { # Standardize txns structure
                    'h24': {'buys': txns_h24.get('buys',0), 'sells': txns_h24.get('sells',0)},
                    'h1': {'buys': txns_h1.get('buys',0), 'sells': txns_h1.get('sells',0)},
                    'm5': {'buys': txns_m5.get('buys',0), 'sells': txns_m5.get('sells',0)}
                },
                "raw_pair_data": pair_to_use # Include the chosen pair data for flexibility
            }

        except Exception as e:
            logger.error(f"Error processing DexScreener data for {mint_address}: {e}")
            logger.error(traceback.format_exc())
            return {"exists": False, "error": f"Error processing DexScreener response: {str(e)}", "should_fallback": True}

    # REMOVED: Moralis API integration has been completely removed
    # We now ONLY use DexScreener API for token data

    async def get_sol_usd_price(self) -> Optional[float]:
        """
        Fetches the current SOL/USD price.
        Uses DexScreener as the sole source.
        """
        now = time.time()
        cache_duration = 300  # Cache for 5 minutes

        # Return cached price if it's fresh enough
        if self.sol_usd_price is not None and (now - self.sol_price_last_update) < cache_duration:
            return self.sol_usd_price

        logger.debug("Fetching fresh SOL/USD price from DexScreener...")
        sol_address = self.SOL_TOKEN_ADDRESS

        # Try DexScreener (sole source)
        dex_data = await self.get_dex_screener_data(sol_address)

        if dex_data and dex_data.get('exists') and dex_data.get('price', 0) > 0:
            self.sol_usd_price = dex_data['price']
            self.sol_price_last_update = now
            logger.debug(f"Updated SOL/USD price from DexScreener: ${self.sol_usd_price}")
            return self.sol_usd_price
        else:
            logger.error("Failed to fetch SOL/USD price from DexScreener.")

            # If we have a cached price that's not too old, use it as last resort
            if self.sol_usd_price is not None and (now - self.sol_price_last_update) < 3600:  # Within last hour
                logger.warning(f"Using slightly stale SOL price from {int(now - self.sol_price_last_update)}s ago: ${self.sol_usd_price}")
                return self.sol_usd_price

            # Fallback to hardcoded value as absolute last resort
            logger.error("DexScreener SOL price source failed, using fallback value")
            return 150.0  # Fallback value

    async def _get_dexscreener_with_timeout(self, token_address: str, is_active_position: bool) -> Dict[str, Any]:
        """Get DexScreener data by calling get_dex_screener_data.
           Timeout and retry logic is now handled within get_dex_screener_data via make_api_request.
        """
        try:
            # The asyncio.timeout wrapper is removed from here.
            # get_dex_screener_data itself now handles robust fetching and retries.
            logger.debug(f"Calling get_dex_screener_data for {token_address} from _get_dexscreener_with_timeout")
            return await self.get_dex_screener_data(token_address, is_active_position) # Corrected indentation
        except asyncio.TimeoutError:
            # This specific TimeoutError catch might be less likely to trigger if make_api_request handles its own timeouts,
            # unless get_dex_screener_data itself has some other unexpected long operation not covered by make_api_request.
            # For safety, keep it, but it should primarily be handled by make_api_request returning None.
            logger.warning(f"_get_dexscreener_with_timeout caught an unexpected asyncio.TimeoutError for {token_address}. This should ideally be handled deeper.")
            return {"exists": False, "error": "Internal Timeout in _get_dexscreener_with_timeout"} # Standardized error return
        except Exception as e:
            logger.error(f"Error in _get_dexscreener_with_timeout calling get_dex_screener_data for {token_address}: {e}")
            logger.error(traceback.format_exc())
            return {"exists": False, "error": str(e)} # Standardized error return

    async def analyze(self, token_address: str, is_active_position: bool = False, extracted_metrics: Optional[Dict[str, Any]] = None, is_gmgn_channel: bool = False) -> Dict[str, Any]:
        """
        Analyzes tokens using DexScreener as the sole data source.

        Args:
            token_address: The token address to analyze
            is_active_position: Whether this token has an active position (affects rate limiting and caching)
            extracted_metrics: Optional metrics extracted from signal message
            is_gmgn_channel: Whether this token is from the GMGN channel (affects validation)
        """
        # Record start time for performance tracking
        analysis_start_time = time.time()
        analysis_result = {
            "exists": False,
            "price": 0.0,
            "price_source": None,
            "confidence": 0.0,
            "sentiment": 0.5,  # Default neutral sentiment
            "reason": "",
            "raw_data": {},
            'market_cap': 0.0,
            'liquidity_usd': 0.0,
            'volume_24h': 0.0,
            'volume_1h': 0.0,
            'volume_5m': 0.0,
            'pair_age_minutes': 0.0,
            'fdv': 0.0,
            'price_change_5m': 0.0,
            'tx_count_24h': 0,
            'name': 'Unknown',
            'symbol': 'N/A'
        }

        try:
            # Always fetch SOL price first, if needed
            if not self.sol_usd_price or (time.time() - self.sol_price_last_update > 300): # Update SOL price every 5 mins
                await self.get_sol_usd_price()

            # Fetch data from DexScreener
            dex_data = await self._get_dexscreener_with_timeout(token_address, is_active_position)

            # Moralis API removed - using DexScreener only for all tokens

            # CRITICAL FIX: Process DexScreener data if we have it, regardless of Moralis status
            # This ensures GMGN tokens get full data processing from both sources
            if dex_data and dex_data.get("exists"):
                # Update or set the analysis result with DexScreener data
                analysis_result["exists"] = True
                analysis_result["price"] = dex_data.get('price', 0)

                # Update price source to reflect combined data if we already have Moralis data
                if analysis_result.get("price_source") == "moralis":
                    analysis_result["price_source"] = "dexscreener+moralis"
                else:
                    analysis_result["price_source"] = "dexscreener"

                analysis_result["raw_data"]["dexscreener"] = dex_data
                analysis_result['market_cap'] = dex_data.get('market_cap', 0)
                dex_liquidity = dex_data.get('liquidity', 0)

                # CRITICAL FIX: Handle liquidity merging for GMGN tokens
                existing_liquidity = analysis_result.get('liquidity_usd', 0)
                if existing_liquidity > 0 and existing_liquidity > dex_liquidity:
                    # Keep existing Moralis liquidity if it's higher
                    logger.info(f"🔧 LIQUIDITY MERGE: Keeping Moralis liquidity (${existing_liquidity:,.2f}) over DexScreener (${dex_liquidity:,.2f})")
                else:
                    # Use DexScreener liquidity
                    analysis_result['liquidity_usd'] = dex_liquidity
                    analysis_result['liquidity'] = dex_liquidity
                    if existing_liquidity > 0:
                        logger.info(f"🔧 LIQUIDITY MERGE: Using DexScreener liquidity (${dex_liquidity:,.2f}) over Moralis (${existing_liquidity:,.2f})")
                    else:
                        logger.debug(f"Using DexScreener liquidity: ${dex_liquidity:,.2f}")
                analysis_result['volume_24h'] = dex_data.get('volume_24h', 0)
                analysis_result['volume_1h'] = dex_data.get('volume_1h', 0) or (dex_data.get('volume_24h', 0) / 24)
                analysis_result['volume_5m'] = dex_data.get('volume_5m', 0) or (analysis_result['volume_1h'] / 12)
                analysis_result['pair_age_minutes'] = dex_data.get('pair_age_minutes', 0)
                analysis_result['fdv'] = dex_data.get('fdv', 0)
                analysis_result['price_change_5m'] = dex_data.get('price_change_5m', 0)
                analysis_result['tx_count_24h'] = (
                    dex_data.get('txns', {}).get('h24', {}).get('buys', 0) +
                    dex_data.get('txns', {}).get('h24', {}).get('sells', 0)
                )
                # CRITICAL FIX: Don't overwrite Moralis token info if we already have it
                if not analysis_result.get('name') or analysis_result.get('name') == 'Unknown':
                    analysis_result['name'] = dex_data.get('name', 'Unknown')
                if not analysis_result.get('symbol') or analysis_result.get('symbol') == 'Unknown':
                    analysis_result['symbol'] = dex_data.get('symbol', 'Unknown')

                # --- START: GMGN Liquidity Fallback ---
                # If DexScreener liquidity is 0 or missing, try to use signal-extracted liquidity
                if not analysis_result['liquidity_usd'] or analysis_result['liquidity_usd'] == 0:
                    if extracted_metrics and 'extracted_liquidity_usd' in extracted_metrics:
                        signal_liq_usd = extracted_metrics['extracted_liquidity_usd']
                        if isinstance(signal_liq_usd, (float, int)) and signal_liq_usd > 0:
                            analysis_result['liquidity_usd'] = signal_liq_usd
                            logger.info(f"Liquidity for {token_address} was 0 from DexScreener. Using signal-extracted liquidity: ${signal_liq_usd:,.2f}")
                            # Update dex_data as well if _calculate_confidence uses it directly
                            dex_data['liquidity'] = signal_liq_usd
                        else:
                            logger.warning(f"Signal-extracted liquidity for {token_address} is invalid or zero: {signal_liq_usd}. Sticking with DexScreener value.")
                    else:
                        logger.info(f"DexScreener liquidity for {token_address} is 0. No signal-extracted liquidity available.")
                # --- END: GMGN Liquidity Fallback ---

                # Log detailed metrics for debugging
                liquidity_source = "DexScreener"
                if analysis_result["price_source"] == "moralis":
                    liquidity_source = "Moralis"
                elif analysis_result["price_source"] == "dexscreener+moralis":
                    liquidity_source = "Moralis+DexScreener"
                elif dex_data.get('liquidity_source') == 'signal':
                    liquidity_source = "Signal Fallback"

                logger.debug(f"Token data for {token_address} (Liquidity source: {liquidity_source}):")
                logger.debug(f"  Name: {analysis_result['name']}, Symbol: {analysis_result['symbol']}")
                logger.debug(f"  Price: ${analysis_result['price']:.8f}, Change 5m: {analysis_result['price_change_5m']}%")
                logger.debug(f"  Volume 24h: ${analysis_result['volume_24h']:,.2f}, 1h: ${analysis_result['volume_1h']:,.2f}, 5m: ${analysis_result['volume_5m']:,.2f}")
                logger.debug(f"  Liquidity: ${analysis_result['liquidity_usd']:,.2f}")
                logger.debug(f"  Market Cap: ${analysis_result['market_cap']:,.2f}, FDV: ${analysis_result['fdv']:,.2f}")
                logger.debug(f"  Pair Age: {analysis_result['pair_age_minutes']:.2f} minutes") # Ensure float for age display
                logger.debug(f"  24h Transactions: {analysis_result['tx_count_24h']}")

                # --- Validation with more detailed logging ---
                liquidity_usd = analysis_result['liquidity_usd']

                # Use approximate SOL price for threshold check if needed
                min_liq_usd_threshold = self.min_liquidity_sol * (self.sol_usd_price or 150.0) # ensure float

                validation_passed = True
                validation_reasons = []

                # Log GMGN channel status for debugging
                if is_gmgn_channel:
                    logger.debug(f"Token {token_address} is from GMGN channel - applying special validation rules")

                # CRITICAL FIX: Age validation moved to rug protection system in token_analyzer.py
                # This ensures rug protection checks run BEFORE any age validation
                logger.debug(f"Age validation bypassed in api_clients.py - will be handled by rug protection system")

                # CRITICAL FIX: Remove early age validation to allow rug protection checks to run
                # The rug protection system in token_analyzer.py will handle age validation properly
                logger.info(f"🛡️ EARLY VALIDATION BYPASS: Skipping age validation in api_clients.py to allow rug protection checks")
                logger.info(f"   Token: {token_address}, Age: {analysis_result['pair_age_minutes']:.2f}m, Liquidity: ${liquidity_usd:.2f}")

                # Only check liquidity here, let rug protection handle age validation
                # Standard liquidity check - skip for GMGN channel tokens
                if not is_gmgn_channel and liquidity_usd < min_liq_usd_threshold:
                    validation_passed = False
                    validation_reasons.append(f"Low liquidity USD (${liquidity_usd:.2f} < ~${min_liq_usd_threshold:.2f})")
                elif is_gmgn_channel and liquidity_usd < min_liq_usd_threshold:
                    # For GMGN tokens, just log the low liquidity but don't fail validation
                    logger.debug(f"GMGN token has low liquidity (${liquidity_usd:.2f} < ~${min_liq_usd_threshold:.2f}) but proceeding with analysis")

                if not validation_passed:
                    reason_str = ", ".join(validation_reasons)
                    logger.debug(f"Validation failed ({token_address}): {reason_str}")
                    analysis_result['reason'] = reason_str
                    analysis_result['confidence'] = 0.0 # Set confidence to 0 if validation fails
                else:
                    logger.debug(f"✅ EARLY VALIDATION PASSED for {token_address} - proceeding to rug protection checks")
                    # SECONDARY FALLBACK: Check if DexScreener data is missing critical fields
                    # If so, try to supplement with Moralis data
                    missing_fields = []
                    if analysis_result.get('price', 0) == 0:
                        missing_fields.append('price')
                    if analysis_result.get('market_cap', 0) == 0:
                        missing_fields.append('market_cap')
                    if analysis_result.get('volume_24h', 0) == 0:
                        missing_fields.append('volume')

                    if missing_fields and self.moralis_client and not is_gmgn_channel:
                        logger.info(f"🔄 SECONDARY FALLBACK: DexScreener missing {missing_fields} for {token_address}, trying Moralis supplement")
                        try:
                            moralis_supplement_data = await asyncio.wait_for(
                                self.moralis_client.get_token_market_data(token_address),
                                timeout=3.0  # Quick timeout for supplement
                            )

                            if moralis_supplement_data.get("success", False):
                                supplemented_fields = []

                                # Supplement missing price
                                if 'price' in missing_fields and moralis_supplement_data.get("currentUsdPrice", 0) > 0:
                                    analysis_result["price"] = moralis_supplement_data.get("currentUsdPrice", 0)
                                    supplemented_fields.append('price')

                                # Supplement missing market cap
                                if 'market_cap' in missing_fields and moralis_supplement_data.get("currentMarketCap", 0) > 0:
                                    analysis_result["market_cap"] = moralis_supplement_data.get("currentMarketCap", 0)
                                    supplemented_fields.append('market_cap')

                                # Supplement missing volume
                                if 'volume' in missing_fields and moralis_supplement_data.get("totalVolume", 0) > 0:
                                    total_volume = moralis_supplement_data.get("totalVolume", 0)
                                    analysis_result["volume_24h"] = total_volume
                                    analysis_result["volume_1h"] = total_volume / 24 if total_volume > 0 else 0
                                    analysis_result["volume_5m"] = total_volume / 288 if total_volume > 0 else 0
                                    supplemented_fields.append('volume')

                                if supplemented_fields:
                                    logger.info(f"✅ SECONDARY FALLBACK SUCCESS: Supplemented {supplemented_fields} from Moralis for {token_address}")
                                    # Update price source to indicate supplementation
                                    if analysis_result.get("price_source") == "dexscreener":
                                        analysis_result["price_source"] = "dexscreener+moralis_supplement"

                                    # Store supplemental data
                                    analysis_result["raw_data"]["moralis_supplement"] = moralis_supplement_data
                                else:
                                    logger.info(f"🔍 SECONDARY FALLBACK: Moralis had no useful supplement data for {token_address}")
                            else:
                                logger.info(f"❌ SECONDARY FALLBACK: Moralis supplement failed for {token_address}")

                        except asyncio.TimeoutError:
                            logger.warning(f"⏰ SECONDARY FALLBACK TIMEOUT: Moralis supplement timed out for {token_address}")
                        except Exception as e:
                            logger.warning(f"💥 SECONDARY FALLBACK ERROR: Moralis supplement error for {token_address}: {e}")

                    # Calculate confidence only if basic validation passes
                    analysis_result['confidence'] = self._calculate_confidence(dex_data, is_gmgn_channel, extracted_metrics)
                    analysis_result['reason'] = "Early validation passed - proceeding to rug protection"


            else: # DexScreener fetch failed or returned no valid data
                logger.warning(f"Failed to get valid data from DexScreener for {token_address}.")

                # FALLBACK: Try Moralis API as fallback when DexScreener fails
                moralis_fallback_success = False
                if self.moralis_client:
                    logger.info(f"🔄 FALLBACK: Trying Moralis API as fallback for {token_address}")
                    try:
                        # Try to get comprehensive market data from Moralis
                        moralis_market_data = await asyncio.wait_for(
                            self.moralis_client.get_token_market_data(token_address),
                            timeout=5.0  # 5 second timeout for fallback
                        )

                        if moralis_market_data.get("success", False):
                            logger.info(f"✅ FALLBACK SUCCESS: Moralis provided data for {token_address}")

                            # Extract the required data points
                            current_price = moralis_market_data.get("currentUsdPrice", 0)
                            current_market_cap = moralis_market_data.get("currentMarketCap", 0)
                            total_volume = moralis_market_data.get("totalVolume", 0)

                            # Also try to get liquidity data
                            moralis_liquidity_data = await asyncio.wait_for(
                                self.moralis_client.get_token_liquidity(token_address),
                                timeout=3.0
                            )
                            total_liquidity_usd = 0
                            if moralis_liquidity_data.get("success", False):
                                total_liquidity_usd = moralis_liquidity_data.get("liquidity_usd", 0)

                            # Update analysis result with Moralis fallback data
                            analysis_result["exists"] = True
                            analysis_result["price"] = current_price
                            analysis_result["market_cap"] = current_market_cap
                            analysis_result["liquidity_usd"] = total_liquidity_usd
                            analysis_result["liquidity"] = total_liquidity_usd  # Ensure both fields are set
                            analysis_result["volume_24h"] = total_volume
                            analysis_result["volume_1h"] = total_volume / 24 if total_volume > 0 else 0  # Estimate
                            analysis_result["volume_5m"] = total_volume / 288 if total_volume > 0 else 0  # Estimate
                            analysis_result["price_source"] = "moralis_fallback"
                            analysis_result["reason"] = "DexScreener failed, using Moralis fallback"

                            # Store raw Moralis data
                            analysis_result["raw_data"]["moralis_fallback"] = moralis_market_data
                            if moralis_liquidity_data.get("success", False):
                                analysis_result["raw_data"]["moralis_liquidity"] = moralis_liquidity_data

                            # Set basic token info if available
                            analysis_result['name'] = 'Unknown (Moralis Fallback)'
                            analysis_result['symbol'] = 'N/A'

                            moralis_fallback_success = True

                            logger.info(f"🎯 MORALIS FALLBACK DATA: Price=${current_price}, MarketCap=${current_market_cap:,.2f}, Volume=${total_volume:,.2f}, Liquidity=${total_liquidity_usd:,.2f}")

                        else:
                            logger.warning(f"❌ FALLBACK FAILED: Moralis API returned no data for {token_address}")

                    except asyncio.TimeoutError:
                        logger.warning(f"⏰ FALLBACK TIMEOUT: Moralis API timed out for {token_address}")
                    except Exception as e:
                        logger.error(f"💥 FALLBACK ERROR: Moralis API error for {token_address}: {e}")

                # If Moralis fallback also failed, set failure state
                if not moralis_fallback_success:
                    analysis_result['reason'] = "Both DexScreener and Moralis fallback failed"
                    analysis_result['confidence'] = 0.0
                    # Ensure 'name' and 'symbol' are set even on failure
                    analysis_result['name'] = 'Unknown (All APIs Failed)'
                    analysis_result['symbol'] = 'N/A'

            # Calculate confidence if we have any valid data (from Moralis or DexScreener)
            if analysis_result["exists"] and analysis_result['confidence'] == 0.0:
                # We have data but haven't calculated confidence yet
                # Use the combined data for confidence calculation
                # CRITICAL FIX: Ensure both liquidity_usd and liquidity fields are properly set
                liquidity_value = analysis_result.get('liquidity_usd', 0) or analysis_result.get('liquidity', 0)
                combined_data = {
                    'liquidity_usd': liquidity_value,
                    'liquidity': liquidity_value,  # Ensure both fields have the same value
                    'market_cap': analysis_result.get('market_cap', 0),
                    'marketCap': analysis_result.get('market_cap', 0),  # Fallback field name
                    'volume_24h': analysis_result.get('volume_24h', 0),
                    'volume_1h': analysis_result.get('volume_1h', 0),
                    'volume_5m': analysis_result.get('volume_5m', 0),
                    'pair_age_minutes': analysis_result.get('pair_age_minutes', 0),
                    'fdv': analysis_result.get('fdv', 0),
                    'price_change_5m': analysis_result.get('price_change_5m', 0),
                    'tx_count_24h': analysis_result.get('tx_count_24h', 0),
                    'txns': analysis_result.get('raw_data', {}).get('dexscreener', {}).get('txns', {}),
                    'symbol': analysis_result.get('symbol', ''),
                    'address': token_address
                }

                logger.info(f"🔧 CONFIDENCE CALCULATION DEBUG: liquidity_value=${liquidity_value}, analysis_result liquidity_usd={analysis_result.get('liquidity_usd', 0)}, analysis_result liquidity={analysis_result.get('liquidity', 0)}")

                logger.info(f"Calculating confidence for {token_address} using combined data")
                analysis_result['confidence'] = self._calculate_confidence(combined_data, is_gmgn_channel, extracted_metrics)
                analysis_result['reason'] = "Analysis completed with combined data"

            # Calculate sentiment (placeholder, as original logic might need adjustment)
            analysis_result['sentiment'] = self._calculate_sentiment() # Consider if this needs dex_data

        except Exception as e: # Corrected indentation to align with the main 'try' in analyze method
            logger.error(f"Error during token analysis for {token_address}: {e}")
            logger.error(traceback.format_exc()) # Add traceback
            analysis_result['reason'] = f"Analysis error: {str(e)}"
            analysis_result['confidence'] = 0.0
            # Ensure 'name' and 'symbol' are set even on critical error
            analysis_result['name'] = 'Unknown (Error)'
            analysis_result['symbol'] = 'N/A'


        # Remove token from active positions if no longer active (e.g., sold)
        if not is_active_position and token_address in self.active_positions:
            self.active_positions.discard(token_address)
            logger.info(f"Removed {token_address} from active positions.")

        # Log analysis duration
        analysis_duration = time.time() - analysis_start_time
        logger.info(f"Token analysis for {token_address} completed in {analysis_duration:.3f}s. Confidence: {analysis_result['confidence']:.2f}. Price: {analysis_result['price']}")

        return analysis_result # Corrected indentation

    async def _api_call_with_retry(self, source: str, url: str, params: Optional[Dict] = None, headers: Optional[Dict] = None) -> Optional[Dict]:
        """Internal helper for making API calls with retry logic."""
        retries = self.api_retries.get(source, 1)
        timeout = self.api_timeouts.get(source, 10)
        delay = self.api_retry_delays.get(source, 1.0)

        for attempt in range(retries):
            try:
                async with self.session.get(url, params=params, headers=headers, timeout=timeout) as resp:
                    if resp.status == 429:
                        retry_after = float(resp.headers.get("Retry-After", delay * (attempt + 1)))
                        logger.warning(f"{source} API rate limit hit. Retrying after {retry_after:.2f}s...")
                        await asyncio.sleep(retry_after)
                        continue
                    resp.raise_for_status()
                    try:
                        data = await resp.json()
                        return data
                    except aiohttp.ContentTypeError:
                        logger.error(f"Failed JSON decode {source} URL {url}. Status: {resp.status}. Response: {await resp.text()}")
                        return None
                    except Exception as json_e:
                        logger.error(f"Error parsing JSON {source}: {json_e}")
                        return None
            except asyncio.TimeoutError:
                logger.warning(f"{source} API request timed out ({timeout}s) URL {url}. Attempt {attempt + 1}/{retries}")
            except aiohttp.ClientResponseError as http_err:
                 logger.error(f"HTTP error {http_err.status} {source} URL {url}: {http_err.message}")
                 if 400 <= http_err.status < 500 and http_err.status != 429: return None
            except aiohttp.ClientError as e:
                logger.error(f"Client error {source} URL {url}: {e}. Attempt {attempt + 1}/{retries}")
            except Exception as e:
                 logger.error(f"Unexpected error during {source} API call {url}: {e}", exc_info=True)
                 return None
            if attempt < retries - 1:
                await asyncio.sleep(delay * (attempt + 1))
        logger.error(f"Failed {source} call {url} after {retries} attempts.")
        return None

    def _calculate_confidence(self, data: Dict[str, Any], is_gmgn_channel: bool = False, extracted_metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        Calculate confidence score based on token metrics.

        This is an enhanced version of the confidence calculation algorithm
        that takes into account more factors and provides a more accurate score.

        Args:
            data: Token data dictionary
            is_gmgn_channel: Whether this token is from the GMGN channel (affects confidence calculation)
            extracted_metrics: Optional metrics extracted from signal message (for future use)
        """
        # Suppress unused parameter warning
        _ = extracted_metrics
        # Initialize with a base confidence
        confidence = 0.5
        factors = {}
        factors["base"] = 0.5

        # Add a boost for GMGN channel tokens
        if is_gmgn_channel:
            confidence += 0.2
            factors["gmgn_channel_boost"] = 0.2
            logger.info(f"Adding confidence boost for GMGN channel token")

        # Check for tokens with suspicious names
        token_name = data.get('symbol', '').upper()
        if re.search(r'^\d+X$', token_name) or re.search(r'^\d+%$', token_name):
            logger.warning(f"Token name '{token_name}' matches a potential scam pattern (e.g., '5X', '100X', '20%')")
            # Apply a stronger penalty for suspicious names to ensure confidence < 0.7
            confidence -= 0.8  # Increased penalty to ensure it's below 0.7
            factors["token_name_warning"] = -0.8

        # PERMANENT FIX: Enhanced liquidity check with multiple field names and fallbacks
        liquidity = 0

        # Try multiple possible liquidity field names
        liquidity_fields = ['liquidity_usd', 'liquidity', 'liquidityUsd', 'liquidity_value', 'usd_liquidity']
        for field in liquidity_fields:
            if field in data and data[field]:
                try:
                    liquidity = float(data[field])
                    if liquidity > 0:
                        logger.debug(f"Found liquidity via field '{field}': ${liquidity:,.2f}")
                        break
                except (ValueError, TypeError):
                    continue

        # CRITICAL FIX: For DexScreener data format (used by active positions), check nested liquidity
        if liquidity == 0 and isinstance(data, dict):
            # Check if this is DexScreener format with nested liquidity
            if 'liquidity' in data and isinstance(data['liquidity'], dict):
                usd_liquidity = data['liquidity'].get('usd', 0)
                if usd_liquidity and usd_liquidity > 0:
                    liquidity = float(usd_liquidity)
                    logger.debug(f"Found liquidity in DexScreener nested format: ${liquidity:,.2f}")

            # Also check for raw_pair_data format
            elif 'raw_pair_data' in data and isinstance(data['raw_pair_data'], dict):
                raw_pair = data['raw_pair_data']
                if 'liquidity' in raw_pair and isinstance(raw_pair['liquidity'], dict):
                    usd_liquidity = raw_pair['liquidity'].get('usd', 0)
                    if usd_liquidity and usd_liquidity > 0:
                        liquidity = float(usd_liquidity)
                        logger.debug(f"Found liquidity in raw_pair_data format: ${liquidity:,.2f}")

        # If still 0, try to extract from raw_data
        if liquidity == 0 and 'raw_data' in data:
            raw_data = data['raw_data']
            if isinstance(raw_data, dict):
                # Check DexScreener data
                if 'dexscreener' in raw_data:
                    dex_data = raw_data['dexscreener']
                    if isinstance(dex_data, dict):
                        # Try various paths in DexScreener data
                        liquidity_paths = [
                            ['liquidity', 'usd'],
                            ['liquidity'],
                            ['pairs', 0, 'liquidity', 'usd'] if 'pairs' in dex_data and dex_data['pairs'] else None
                        ]
                        for path in liquidity_paths:
                            if path is None:
                                continue
                            try:
                                value = dex_data
                                for key in path:
                                    if isinstance(value, dict) and key in value:
                                        value = value[key]
                                    elif isinstance(value, list) and isinstance(key, int) and len(value) > key:
                                        value = value[key]
                                    else:
                                        value = None
                                        break
                                if value is not None:
                                    liquidity = float(value)
                                    if liquidity > 0:
                                        logger.debug(f"Found liquidity in raw_data via path {' -> '.join(map(str, path))}: ${liquidity:,.2f}")
                                        break
                            except (ValueError, TypeError, KeyError, IndexError):
                                continue

        logger.debug(f"Final liquidity for confidence calculation: ${liquidity:,.2f}")

        # Apply liquidity scoring
        if liquidity >= 10000:
            confidence += 0.2
            factors["liquidity"] = 0.2
        elif liquidity >= 5000:
            confidence += 0.1
            factors["liquidity"] = 0.1
        elif liquidity < 1000 and not is_gmgn_channel:  # Don't penalize GMGN tokens for low liquidity
            confidence -= 0.2
            factors["liquidity"] = -0.2
        else:
            factors["liquidity"] = 0.0

        # Volume check
        volume_24h = data.get('volume_24h', 0)
        volume_1h = data.get('volume_1h', 0) or (volume_24h / 24)
        volume_5m = data.get('volume_5m', 0) or (volume_1h / 12)

        # For GMGN channel tokens, skip volume penalties but keep bonuses
        if is_gmgn_channel:
            logger.info(f"GMGN channel token detected in confidence calculation - skipping volume penalties")
            # Check both 1h and 5m volume for a more comprehensive assessment
            if volume_1h >= 5000:
                confidence += 0.2
                factors["volume_1h"] = 0.2
            elif volume_1h >= 1000:
                confidence += 0.1
                factors["volume_1h"] = 0.1
            else:
                factors["volume_1h"] = 0.0  # No penalty for GMGN tokens

            # Add 5m volume check for recent activity
            if volume_5m >= 1000:
                confidence += 0.1
                factors["volume_5m"] = 0.1
            else:
                factors["volume_5m"] = 0.0  # No penalty for GMGN tokens
        else:
            # Standard volume checks for non-GMGN tokens
            # Check both 1h and 5m volume for a more comprehensive assessment
            if volume_1h >= 5000:
                confidence += 0.2
                factors["volume_1h"] = 0.2
            elif volume_1h >= 1000:
                confidence += 0.1
                factors["volume_1h"] = 0.1
            elif volume_1h < 500:
                confidence -= 0.2
                factors["volume_1h"] = -0.2
            else:
                factors["volume_1h"] = 0.0

            # Add 5m volume check for recent activity
            if volume_5m >= 1000:
                confidence += 0.1
                factors["volume_5m"] = 0.1
            elif volume_5m < 100:
                confidence -= 0.1
                factors["volume_5m"] = -0.1
            else:
                factors["volume_5m"] = 0.0

        # Transaction count check - use multiple possible field names
        tx_count = data.get('tx_count_24h', 0) or data.get('txCount', 0)
        # Also check 5m transactions for recent activity
        txns_5m = data.get('txns', {}).get('m5', {})
        buys_5m = txns_5m.get('buys', 0)
        sells_5m = txns_5m.get('sells', 0)
        total_5m_txns = buys_5m + sells_5m

        # Use the higher of 24h or 5m transaction counts for scoring
        effective_tx_count = max(tx_count, total_5m_txns)

        if effective_tx_count >= 100:
            confidence += 0.1
            factors["tx_count"] = 0.1
        elif effective_tx_count >= 50:
            confidence += 0.05
            factors["tx_count"] = 0.05
        elif effective_tx_count < 10:
            confidence -= 0.1
            factors["tx_count"] = -0.1
        else:
            factors["tx_count"] = 0.0

        # Age check with liquidity consideration
        age = data.get('pair_age_minutes', 0)
        liquidity = data.get('liquidity_usd', 0) or data.get('liquidity', 0)

        # For GMGN channel tokens, skip age penalties but keep bonuses
        if is_gmgn_channel:
            logger.info(f"GMGN channel token detected in confidence calculation - skipping age penalties")
            # For GMGN tokens, give a small boost for tokens in the ideal age range
            if 5 <= age <= 60:  # 5 minutes to 1 hour
                confidence += 0.2
                factors["age"] = 0.2
            else:
                # No penalty for age for GMGN tokens
                factors["age"] = 0.0
        else:
            # Standard age check for normal liquidity
            if 5 <= age <= 60:  # 5 minutes to 1 hour
                confidence += 0.2
                factors["age"] = 0.2
            elif age < 2:
                # For very new tokens, consider liquidity as a mitigating factor
                if liquidity >= 30000:  # Very high liquidity
                    # Don't penalize, give a small boost instead
                    confidence += 0.1
                    factors["age"] = 0.1
                    factors["high_liquidity_new_token_boost"] = 0.1
                    logger.info(f"New token ({age:.2f}m) with high liquidity (${liquidity:.2f}) - applying confidence boost")
                elif liquidity >= 15000:  # High liquidity
                    # Reduce the penalty
                    confidence -= 0.05
                    factors["age"] = -0.05
                    factors["high_liquidity_new_token_boost"] = 0.15  # Reduced from standard -0.2 penalty
                    logger.info(f"New token ({age:.2f}m) with good liquidity (${liquidity:.2f}) - reducing age penalty")
                else:
                    # Standard penalty for low liquidity new tokens
                    confidence -= 0.2
                    factors["age"] = -0.2
            elif age > 300:  # Penalize tokens older than 5 hours (300 minutes)
                confidence -= 0.3
                factors["age"] = -0.3
            else:
                factors["age"] = 0.0

        # FDV check
        fdv = data.get('fdv', 0)
        if 100000 <= fdv <= 1000000:
            confidence += 0.1
            factors["fdv"] = 0.1
        elif fdv > 10000000:
            confidence -= 0.1
            factors["fdv"] = -0.1
        else:
            factors["fdv"] = 0.0

        # Price change check
        price_change = data.get('price_change_5m', 0)
        if price_change > 50:  # Over 50% gain in 5 minutes
            confidence += 0.1
            factors["price_change"] = 0.1
        elif price_change < -20:  # Over 20% loss in 5 minutes
            confidence -= 0.2
            factors["price_change"] = -0.2
        else:
            factors["price_change"] = 0.0

        # Buy/Sell ratio check - prioritize 5m data for recent activity
        txns = data.get('txns', {})
        # Try 5m first, then fall back to 1h
        buys_5m = txns.get('m5', {}).get('buys', 0)
        sells_5m = txns.get('m5', {}).get('sells', 0)
        buys_1h = txns.get('h1', {}).get('buys', 0)
        sells_1h = txns.get('h1', {}).get('sells', 0)

        # Use 5m data if available, otherwise use 1h data
        buys = buys_5m if buys_5m > 0 else buys_1h
        sells = sells_5m if sells_5m > 0 else sells_1h

        if buys > 0 and sells > 0:
            ratio = buys / sells
            if ratio > 2:  # More than 2x buys than sells
                confidence += 0.1
                factors["buy_sell_ratio"] = 0.1
            elif ratio < 0.5:  # More than 2x sells than buys
                confidence -= 0.1
                factors["buy_sell_ratio"] = -0.1
            else:
                factors["buy_sell_ratio"] = 0.0
        elif buys > 0 and sells == 0:  # Only buys, very bullish
            confidence += 0.15
            factors["buy_sell_ratio"] = 0.15
        else:
            factors["buy_sell_ratio"] = 0.0

        # Market cap check - use both possible field names
        mcap = data.get('market_cap', 0) or data.get('marketCap', 0)
        if 0 < mcap < 50000:
            confidence += 0.05
            factors["mcap"] = 0.05
        elif mcap > 1000000:
            confidence -= 0.1
            factors["mcap"] = -0.1
        else:
            factors["mcap"] = 0.0

        # Log the factors for debugging
        logger.info(f"Confidence calculation for {data.get('address', 'unknown')}:")
        logger.info(f"  Input data: liquidity_usd={data.get('liquidity_usd', 0)}, market_cap={data.get('market_cap', 0)}, volume_5m={data.get('volume_5m', 0)}")
        logger.info(f"  Transactions 5m: buys={buys_5m}, sells={sells_5m}, ratio={buys_5m/max(sells_5m,1):.2f}")
        logger.info(f"  Price change 5m: {data.get('price_change_5m', 0)}%")
        logger.info(f"  Age: {age:.2f} minutes")
        for factor, value in factors.items():
            logger.info(f"  {factor}: {value}")
        logger.info(f"  Final confidence: {confidence:.3f}")

        # Clamp confidence to [0.0, 1.0]
        final_confidence = max(0.0, min(1.0, confidence))

        return round(final_confidence, 3)

    def _parse_numeric_value(self, value_str: str) -> float:
        """
        Parse a numeric value from a string that might include K, M, B suffixes.

        Args:
            value_str: String representation of a number, e.g., "10K", "1.5M", "$500", "26.4K", "27.93"

        Returns:
            Numeric value as a float
        """
        if not value_str:
            return 0.0

        try:
            # Remove $ and commas
            clean_str = value_str.replace('$', '').replace(',', '').strip()

            # Special case for liquidity values like "27.93" which should be 27930 (27.93K)
            # Check if this is a liquidity value without K suffix but should be interpreted as K
            if '.' in clean_str and not any(suffix in clean_str.upper() for suffix in ['K', 'M', 'B']):
                # If it's a small number (less than 100) and has decimals, it might be in K
                try:
                    float_val = float(clean_str)
                    if float_val < 100:
                        logger.info(f"Interpreting liquidity value '{value_str}' as {float_val * 1000:,.2f} (assuming K suffix)")
                        return float_val * 1000
                except ValueError:
                    pass  # Continue with normal parsing

            # Handle K, M, B suffixes
            multiplier = 1
            if clean_str.upper().endswith('K'):
                multiplier = 1000
                clean_str = clean_str[:-1]
            elif clean_str.upper().endswith('M'):
                multiplier = 1000000
                clean_str = clean_str[:-1]
            elif clean_str.upper().endswith('B'):
                multiplier = 1000000000
                clean_str = clean_str[:-1]

            # Convert to float and apply multiplier
            return float(clean_str) * multiplier
        except (ValueError, TypeError) as e:
            logger.warning(f"Failed to parse numeric value '{value_str}': {e}")
            return 0.0

    def _calculate_sentiment(self) -> float:
        """Placeholder for sentiment analysis.

        Returns:
            Sentiment score between 0.0 and 1.0
        """
        # This is a placeholder implementation
        # Future implementation will use token address and data
        return 0.5 # Neutral

    def format_token_metrics(self, token_data: dict) -> str:
        """Format token metrics into a readable string."""
        if not token_data: return "No token data available"
        lines = []
        lines.append("=== Token Analysis Results ===")

        # Basic token info
        if token_data.get('name'):
            lines.append(f"Name: {token_data.get('name', 'Unknown')}")
        if token_data.get('symbol'):
            lines.append(f"Symbol: {token_data.get('symbol', 'UNKNOWN')}")

        # Price and source
        lines.append(f"Price: ${token_data.get('price', 0):.8f}")
        lines.append(f"Source: {token_data.get('price_source', 'N/A')}")
        lines.append(f"Confidence: {token_data.get('confidence', 0):.2f}")

        # Market metrics
        if token_data.get('market_cap', 0) > 0:
            lines.append(f"Market Cap: ${token_data.get('market_cap', 0):,.2f}")
        if token_data.get('fdv', 0) > 0:
            lines.append(f"Fully Diluted Value: ${token_data.get('fdv', 0):,.2f}")

        # Liquidity
        if token_data.get('liquidity_usd_value', 0) > 0:
            lines.append(f"Liquidity (USD): ${token_data.get('liquidity_usd_value', 0):,.2f}")
        elif token_data.get('liquidity_usd', 0) > 0:
            # Try to parse the liquidity value if it's a string
            try:
                if isinstance(token_data.get('liquidity_usd'), str):
                    liq_value = token_data.get('liquidity_usd')
                    if liq_value.replace('.', '').isdigit() and float(liq_value) < 100 and '.' in liq_value:
                        lines.append(f"Liquidity (USD): ${float(liq_value) * 1000:,.2f} (adjusted from ${liq_value})")
                    else:
                        lines.append(f"Liquidity (USD): ${token_data.get('liquidity_usd', 0):,.2f}")
                else:
                    lines.append(f"Liquidity (USD): ${token_data.get('liquidity_usd', 0):,.2f}")
            except Exception:
                lines.append(f"Liquidity (USD): ${token_data.get('liquidity_usd', 0):,.2f}")

        if token_data.get('liquidity_sol', 0) > 0:
            lines.append(f"Liquidity (SOL): {token_data.get('liquidity_sol', 0):,.2f} SOL")

        # Volume
        if token_data.get('volume_5m', 0) > 0:
            lines.append(f"5m Volume: ${token_data.get('volume_5m', 0):,.2f}")
        if token_data.get('volume24h', 0) > 0:
            lines.append(f"24h Volume: ${token_data.get('volume24h', 0):,.2f}")

        # Age and transactions
        if token_data.get('pair_age_minutes', 0) > 0:
            lines.append(f"Pair Age: {token_data.get('pair_age_minutes', 0)} minutes")
        if token_data.get('tx_count_24h', 0) > 0:
            lines.append(f"24h Transactions: {token_data.get('tx_count_24h', 0)}")

        # Transaction details from DexScreener
        raw_data = token_data.get('raw_data', {})
        if isinstance(raw_data, dict) and 'txns' in raw_data:
            txns = raw_data.get('txns', {})
            if 'h1' in txns:
                h1 = txns.get('h1', {})
                buys_1h = h1.get('buys', 0)
                sells_1h = h1.get('sells', 0)
                if buys_1h > 0 or sells_1h > 0:
                    lines.append(f"1h Transactions: {buys_1h} buys, {sells_1h} sells")

            if 'h24' in txns:
                h24 = txns.get('h24', {})
                buys_24h = h24.get('buys', 0)
                sells_24h = h24.get('sells', 0)
                if buys_24h > 0 or sells_24h > 0:
                    lines.append(f"24h Transactions: {buys_24h} buys, {sells_24h} sells")

        # Holders (from PumpPortal or other sources)
        if token_data.get('holders', 0) > 0:
            lines.append(f"Holders Count: {token_data.get('holders', 0)}")

        # Price change data
        if raw_data and 'price_change_5m' in raw_data:
            price_change = raw_data.get('price_change_5m', 0)
            if price_change != 0:
                sign = '+' if price_change > 0 else ''
                lines.append(f"5m Price Change: {sign}{price_change:.2f}%")

        # Safety flags
        if token_data.get('safety_flags'):
            lines.append(f"Safety Flags: {', '.join(token_data.get('safety_flags', []))}")

        # Raw data display
        if raw_data:
            lines.append("\n--- Raw Token Data ---")
            # Display the most important raw data fields
            if isinstance(raw_data, dict):
                for key in ['name', 'symbol', 'price', 'volume_24h', 'liquidity', 'marketCap', 'fdv', 'pairAge']:
                    if key in raw_data:
                        lines.append(f"{key}: {raw_data[key]}")

        lines.append("===========================")
        return "\n".join(lines)

    # --- Placeholder methods for CLI options ---
    async def check_api_health(self) -> Dict[str, str]:
        """Check the health/status of configured APIs."""
        logger.info("Checking API health status...")
        status = {}

        # Initialize sessions if needed
        if not self.session or self.session.closed:
            await self.init_sessions()

        # Check DexScreener API
        try:
            dexscreener_endpoint = self.config_manager.get('api_endpoints', 'dexscreener', default="https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}")
            dexscreener_url = dexscreener_endpoint.replace("{tokenAddress}", "So11111111111111111111111111111111111111112").replace("{chainId}", "solana")
            async with self.session.get(dexscreener_url, timeout=10) as response:
                if response.status == 200:
                    status["dexscreener"] = "OK"
                    try:
                        data = await response.json()
                        if data and 'pairs' in data and len(data['pairs']) > 0:
                            first_token = data['pairs'][0]['baseToken']['symbol']
                            status["dexscreener"] = f"OK (Found {len(data['pairs'])} pairs, first token: {first_token})"
                    except Exception as e:
                        logger.warning(f"Could not parse DexScreener response: {e}")
                else:
                    status["dexscreener"] = f"Error: HTTP {response.status}"
                    logger.error(f"DexScreener API returned HTTP {response.status}")
        except Exception as e:
            logger.error(f"Error checking DexScreener API: {e}")
            status["dexscreener"] = f"Error: {str(e)}"

        # API integrations removed
        status["moralis_price"] = "REMOVED"
        status["moralis_pairs"] = "REMOVED"
        status["pumpportal"] = "REMOVED"

        # Check CoinGecko API as a fallback for SOL price (or general health check)
        try:
            coingecko_url = "https://api.coingecko.com/api/v3/ping"
            async with self.session.get(coingecko_url, timeout=10) as response:
                if response.status == 200:
                    status["coingecko"] = "OK"
                else:
                    status["coingecko"] = f"Error: HTTP {response.status}"
        except Exception as e:
            logger.error(f"Error checking CoinGecko API: {e}")
            status["coingecko"] = f"Error: {str(e)}"

        return status

    async def clear_cache(self, older_than_seconds: Optional[float] = None) -> int:
        """Clear the internal API cache."""
        logger.info(f"Clearing API cache older than {older_than_seconds} seconds")
        if not hasattr(self, 'rate_limiter') or self.rate_limiter is None:
            logger.warning("No rate limiter available to clear cache")
            return 0

        try:
            # Since rate_limiter.clear_cache is synchronous, we don't need to await it
            # But we can run it in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            cleared_count = await loop.run_in_executor(
                None,
                lambda: self.rate_limiter.clear_cache(older_than_seconds)
            )
            logger.info(f"Cleared {cleared_count} items from API cache")
            return cleared_count
        except Exception as e:
            logger.error(f"Error clearing API cache: {e}")
            return 0

    async def reset_stats(self) -> bool:
        """Reset API usage statistics."""
        logger.info("Resetting API usage statistics")
        if not hasattr(self, 'rate_limiter') or self.rate_limiter is None:
            logger.warning("No rate limiter available to reset stats")
            return False

        try:
            # Since rate_limiter.reset_stats is synchronous, we don't need to await it
            # But we can run it in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.rate_limiter.reset_stats()
            )
            logger.info("API usage statistics reset successfully")
            return True
        except Exception as e:
            logger.error(f"Error resetting API stats: {e}")
            return False

    async def get_usage_stats(self) -> Dict[str, Any]:
        """Get API usage statistics."""
        if not hasattr(self, 'rate_limiter') or self.rate_limiter is None:
            logger.warning("No rate limiter available to get stats")
            return {}

        try:
            # Since rate_limiter.get_usage_stats is synchronous, we don't need to await it
            # But we can run it in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            stats = await loop.run_in_executor(
                None,
                lambda: self.rate_limiter.get_usage_stats()
            )
            return stats
        except Exception as e:
            logger.error(f"Error getting API stats: {e}")
            return {}

    async def get_price_history(self, token_address: str) -> List[float]:
        """Get price history for a token.

        Args:
            token_address: The token address to get price history for

        Returns:
            List of historical prices
        """
        if token_address in self.price_history:
            return self.price_history[token_address]
        return []

    async def get_volume_history(self, token_address: str) -> List[float]:
        """Get volume history for a token.

        Args:
            token_address: The token address to get volume history for

        Returns:
            List of historical volumes
        """
        if token_address in self.volume_history:
            return self.volume_history[token_address]
        return []

    def get_cached_token_data(self, token_address: str) -> Dict[str, Any]:
        """Get cached token data for a token address.

        This method is used by BotController to get price and volume history.

        Args:
            token_address: The token address to get cached data for

        Returns:
            Dict containing cached token data or empty dict if not in cache
        """
        logger.debug(f"Getting cached token data for {token_address}")

        # First check in price_history and volume_history
        if token_address in self.price_history:
            logger.debug(f"Found token {token_address} in price_history")
            result = {
                "price_history": self.price_history.get(token_address, []),
                "volume_history": self.volume_history.get(token_address, [])
            }
            return result

        # Then check rate limiter cache for DexScreener data
        dex_cache_key = f"dexscreener_search_{token_address}"
        if hasattr(self, 'rate_limiter') and dex_cache_key in self.rate_limiter.cache:
            logger.debug(f"Found token {token_address} in DexScreener cache")
            dex_data = self.rate_limiter.cache[dex_cache_key]['data']

            # Extract pairs data if available
            if 'pairs' in dex_data:
                pairs = dex_data['pairs']
                if pairs and len(pairs) > 0:
                    # Create a structured result with the data BotController expects
                    result = {
                        "raw_data": {
                            "dexscreener": dex_data
                        },
                        "price_history": [float(pairs[0].get('priceUsd', 0))],
                        "volume_history": [float(pairs[0].get('volume', {}).get('h1', 0) / 12)]  # Estimate 5m volume
                    }
                    return result

            # If we have dex_data but not in the expected format, return it as is
            return {"raw_data": {"dexscreener": dex_data}}

        # Check for cached analysis results from previous analyze() calls
        if hasattr(self, 'analysis_cache') and token_address in self.analysis_cache:
            logger.debug(f"Found token {token_address} in analysis cache")
            return self.analysis_cache[token_address]

        # Check for other cache keys that might contain the token address
        for cache_key in self.rate_limiter.cache:
            if token_address in cache_key:
                logger.debug(f"Found token {token_address} in cache with key {cache_key}")
                return self.rate_limiter.cache[cache_key]['data']

        # If nothing found, return empty dict
        logger.debug(f"No cached data found for token {token_address}")
        return {}

    async def update_price_history(self, token_address: str, price: float):
        """Update price history for a token.

        Args:
            token_address: The token address to update price history for
            price: The current price to add to history
        """
        if token_address not in self.price_history:
            self.price_history[token_address] = []

        self.price_history[token_address].append(price)

        # Trim history if it gets too long
        if len(self.price_history[token_address]) > self.max_history_length:
            self.price_history[token_address] = self.price_history[token_address][-self.max_history_length:]

    async def update_volume_history(self, token_address: str, volume: float):
        """Update volume history for a token.

        Args:
            token_address: The token address to update volume history for
            volume: The current volume to add to history
        """
        if token_address not in self.volume_history:
            self.volume_history[token_address] = []

        self.volume_history[token_address].append(volume)

        # Trim history if it gets too long
        if len(self.volume_history[token_address]) > self.max_history_length:
            self.volume_history[token_address] = self.volume_history[token_address][-self.max_history_length:]

    async def cleanup(self):
        """Cleanup resources with proper error handling"""
        try:
            # PumpPortal has been disabled per user request

            # Close the main session if it exists and is not already closed
            if self.session and not self.session.closed:
                try:
                    await self.session.close()
                    logger.info("Closed main aiohttp session")
                except Exception as e:
                    logger.error(f"Error closing main session: {e}")

            # Clear all caches and data structures
            self.price_data.clear()
            self.last_price_update.clear()
            self.watchlist_tokens.clear()
            self.price_history.clear()
            self.volume_history.clear()
            self.analysis_cache.clear()

            # Clear sessions dictionary
            self.sessions.clear()

            # Clear rate limiter cache
            if hasattr(self, 'rate_limiter') and self.rate_limiter:
                try:
                    # Use the async method we just fixed
                    await self.clear_cache()
                    logger.info("Cleared rate limiter cache")
                except Exception as e:
                    logger.error(f"Error clearing rate limiter cache: {e}")

            logger.info("TokenAnalyzer cleanup completed")
        except Exception as e:
            logger.error(f"Error during TokenAnalyzer cleanup: {e}", exc_info=True)

    # PumpPortal integration removed

# GmgnTrader class removed - replaced by PumpPortal trader