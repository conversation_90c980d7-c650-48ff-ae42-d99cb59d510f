import asyncio
import logging
import os
import time
import traceback
import aiohttp
import random
from datetime import datetime
from typing import Dict, Any, Optional, List

# OPTIMIZED: Removed circular import - cache functions now handled locally

from config_manager import Config<PERSON>anager
from state_manager import StateManager
from token_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from signal_handler import SignalHandler
from trade_executor import TradeExecutor
from websocket_manager import WebSocketManager
from pumpportal_trader import PumpPortalTrader
# pump_fun_analyzer removed - functionality integrated into simple_pump_analyzer
from analysis_worker_pool import AnalysisWorkerPool
from execution_queue import TradeExecutionQueue

logger = logging.getLogger(__name__)

# OPTIMIZED: Local cache functions to replace circular import
_local_token_analysis_cache = {}

def should_analyze_token(token_address, _reason=None):
    """Local cache function to check if token should be analyzed"""
    import time
    current_time = time.time()

    if token_address not in _local_token_analysis_cache:
        return True, "New token"

    cached_data = _local_token_analysis_cache[token_address]
    time_since_last = current_time - cached_data["timestamp"]
    last_reason = cached_data.get("reason", "")

    # Smart intervals based on rejection reason
    if "low_liquidity" in last_reason.lower() or "liquidity" in last_reason.lower():
        min_interval = 300  # 5 minutes for liquidity issues
    elif "rejected" in last_reason.lower() or "risk" in last_reason.lower():
        min_interval = 120  # 2 minutes for other rejections
    else:
        min_interval = 30   # 30 seconds minimum for any analysis

    if time_since_last < min_interval:
        return False, f"Recently analyzed ({time_since_last:.1f}s ago): {last_reason}"

    return True, "Cache expired"

def cache_token_analysis(token_address, result, reason=None):
    """Local cache function to store token analysis result"""
    import time
    _local_token_analysis_cache[token_address] = {
        "timestamp": time.time(),
        "result": result,
        "reason": reason
    }

class BotController:
    """Main controller for the trading bot"""

    def __init__(self, config: ConfigManager, state: StateManager, name: str = "SolanaTrader"):
        self.config = config
        self.state = state
        self.name = name
        self.is_trading = False
        self.current_strategy = "default"

        # Initialize status and run_mode (MAINNET ONLY)
        self.status = "STOPPED"
        self.run_mode = "REAL"  # Real trading only
        self.network_mode = "MAINNET"  # MAINNET ONLY

        # SURGICAL FIX: Load trading settings from config - correct path
        self.trading_settings = config.get_section('transaction_settings')
        if not self.trading_settings:
            # Fallback defaults
            self.trading_settings = {
                "slippage_percent": 1.0,
                "gas_price_sol": 0.000005,
                "platform_fee_percent": 0.3
            }
        logger.info(f"Loaded transaction settings: {self.trading_settings}")

        # Initialize components
        self.token_analyzer = TokenAnalyzer(config)
        self.signal_handler = SignalHandler(config)

        # BULLETPROOF: Separate functions for analysis vs monitoring
        self.fast_analysis_function = None      # For NEW signals only
        self.position_monitoring_function = None  # For bought positions only

        # pump_fun_analyzer removed - functionality integrated into simple_pump_analyzer
        # All pump.fun token analysis now handled by fast analysis function
        logger.info("Pump.fun analysis integrated into fast analysis pipeline")

        # Initialize parallel pipeline components
        self.analysis_worker_pool = None  # Will be initialized in start_trading
        self.num_analysis_workers = config.get('trading_settings', 'num_analysis_workers', default=3)
        self.num_execution_workers = config.get('trading_settings', 'num_execution_workers', default=2)

        # YAML config is deprecated - we use finalconfig.json instead
        logger.info("YAML configuration is deprecated. Using finalconfig.json for all settings.")

        # Initialize PumpPortal trader for real mode
        self.pumpportal_trader = PumpPortalTrader(config)
        # CRITICAL ENHANCEMENT: Initialize unified trader for multi-network support
        from unified_trader import UnifiedTrader
        self.unified_trader = UnifiedTrader(config, state, self)  # CRITICAL FIX: Pass bot controller for adaptive slippage

        self.trade_executor = TradeExecutor(config, state, self.pumpportal_trader)

        # Add token_analyzer to trade_executor for price logging
        self.trade_executor.token_analyzer = self.token_analyzer

        # DISABLED: Don't add unified trader to trade executor - use direct PumpPortal for reliability
        # self.trade_executor.unified_trader = self.unified_trader

        # CRITICAL FIX: Initialize execution queue immediately for sell pipeline
        from execution_queue import TradeExecutionQueue
        self.execution_queue = TradeExecutionQueue(
            self.trade_executor,
            num_workers=self.num_execution_workers
        )
        logger.info(f"✅ Execution queue initialized with {self.num_execution_workers} workers")

        # PERMANENT FIX: Start execution queue workers immediately to ensure sells always work
        # This ensures that sells work even when trading is "stopped" (manual sells, position monitoring, etc.)
        self._execution_queue_start_task = None

        self.websocket_manager = WebSocketManager(config)

        # Initialize unified trader asynchronously (will be called later)
        self._unified_trader_initialized = False

        # Using fundamental analysis only
        self.technical_analyzer = None

        # Initialize logging
        self.logger = logging.getLogger('bot_controller')

        # Runtime state
        self._stop_event = asyncio.Event()
        self._monitor_task: Optional[asyncio.Task] = None
        self._signal_task: Optional[asyncio.Task] = None
        # Real trading only - no simulation mode

        # Position monitoring resilience
        self._monitor_startup_attempts = 0
        self._max_monitor_startup_attempts = 5
        self._monitor_restart_delay = 2.0
        self._monitor_health_check_interval = 30.0  # Check monitor health every 30 seconds
        self._last_monitor_health_check = 0
        self._monitor_consecutive_errors = 0
        self._max_monitor_consecutive_errors = 3

        # Load Trading Settings from both JSON and YAML configs
        self.trade_settings = self.config.get_trading_settings()

        # CRITICAL FIX: Load sol_per_trade from state if available, otherwise from config
        # Check if we have a saved value in state
        if hasattr(self.state, 'sol_per_trade') and self.state.sol_per_trade > 0:
            self.sol_per_trade = self.state.sol_per_trade
            logger.info(f"Loaded SOL per trade from state: {self.sol_per_trade} SOL")
        else:
            # FIXED: Load sol_trade_amount from root level of trading_settings first
            self.sol_per_trade = self.trade_settings.get('sol_trade_amount', 0.16)  # Load from finalconfig.json line 79

            # Then check strategy-specific override if available
            strategy_name = self.current_strategy.upper()
            strategies = self.config.get_section('trading_settings').get('strategies', {})
            strategy_params = strategies.get(strategy_name, {})

            if strategy_params and 'sol_trade_amount' in strategy_params:
                self.sol_per_trade = strategy_params.get('sol_trade_amount')
                logger.info(f"Using strategy-specific SOL per trade: {self.sol_per_trade} SOL (from {strategy_name} strategy)")
            else:
                logger.info(f"Using root-level SOL per trade from config: {self.sol_per_trade} SOL (from finalconfig.json line 79)")

        # Load confidence threshold from strategy
        strategy_name = self.current_strategy.upper()
        trading_settings = self.config.get_section('trading_settings')
        strategies = trading_settings.get('strategies', {})
        strategy_params = strategies.get(strategy_name, {})

        if strategy_params and 'sl_high_confidence_threshold' in strategy_params:
            self.min_confidence = strategy_params.get('sl_high_confidence_threshold', 0.7)
        else:
            self.min_confidence = self.trade_settings.get('confidence_threshold', 0.7)

        # FIXED: Read concurrent trades and timeout from finalconfig.json
        self.max_concurrent_trades = self.trade_settings.get('max_concurrent_trades', 1)  # Fixed default to 1
        self.max_hold_time_minutes = self.trade_settings.get('max_hold_time_minutes', 3)  # Fixed default to 3
        self.cooldown_seconds = self.trade_settings.get('cooldown_period_seconds', 300)

        # CRITICAL FIX: Initialize monitoring_interval from config
        self.monitoring_interval = self.config.get('trading_settings', 'position_monitor_interval_seconds', default=1.5)

        logger.info(f"✅ ROBUST POSITION MONITORING: max_concurrent_trades={self.max_concurrent_trades}, "
                   f"max_hold_time_minutes={self.max_hold_time_minutes}, monitoring_interval={self.monitoring_interval}s (from finalconfig.json)")

        # Dynamic position sizing settings
        self.min_position_size_percent = 0.5  # Minimum position size as percentage of base size
        self.max_position_size_percent = 1.2  # Maximum position size as percentage of base size

        self.slippage_bps = self.config.get_slippage_bps()

        # Load TP/SL Settings from JSON config
        try:
            self.tp_sl_settings = self.config.get_tp_sl_settings()
            logger.info(f"Loaded TP/SL Settings: {self.tp_sl_settings}")
        except Exception as e:
            logger.warning(f"Error loading TP/SL settings: {e}")
            # Set default TP/SL settings
            self.tp_sl_settings = {
                'pnl_sell_percent_min': 15.0,
                'pnl_sell_percent_max': 25.0,
                'partial_sell_fraction': 0.8,
                'stop_loss_percent': -30.0
            }
            logger.info(f"Using default TP/SL Settings: {self.tp_sl_settings}")

        # Load Risk Detection Thresholds from JSON config
        try:
            self.risk_thresholds = self.config.get_risk_detection_thresholds()
            logger.info(f"Loaded Risk Detection Thresholds: {self.risk_thresholds}")
        except Exception as e:
            logger.warning(f"Error loading risk detection thresholds: {e}")
            # Set default risk detection thresholds
            self.risk_thresholds = {
                'critical_pnl_percent': -12.0,
                'critical_profit_percent': 40.0,
                'critical_volatility': 0.08,
                'critical_drawdown_percent': -8.0,
                'critical_age_minutes': 1.5,
                'high_pnl_loss_percent': -8.0,
                'high_pnl_profit_percent': 25.0,
                'high_volatility': 0.04,
                'high_drawdown_percent': -4.0,
                'high_age_minutes': 3.0,
                'high_profit_drop_threshold': 1.15,
                'high_profit_drop_ratio': 0.92,
                'medium_pnl_loss_percent': -4.0,
                'medium_pnl_profit_percent': 12.0,
                'medium_volatility': 0.015,
                'medium_age_minutes': 10.0,
                'medium_profit_drop_threshold': 1.08,
                'medium_profit_drop_ratio': 0.96
            }
            logger.info(f"Using default Risk Detection Thresholds: {self.risk_thresholds}")

        # Load rug protection settings from finalconfig.json
        try:
            trading_settings = self.config.get_section('trading_settings')
            rug_protection_settings = trading_settings.get('rug_protection', {})
            self.min_liquidity_usd = rug_protection_settings.get('min_liquidity_usd', 8000.0)  # SURGICAL FIX: Correct path
            logger.info(f"Loaded min_liquidity_usd from trading_settings.rug_protection: ${self.min_liquidity_usd}")
        except Exception as e:
            logger.warning(f"Error loading rug protection settings: {e}")
            self.min_liquidity_usd = 8000.0  # SURGICAL FIX: Updated default fallback
            logger.info(f"Using default min_liquidity_usd: ${self.min_liquidity_usd}")

        # Initialize portfolio risk management
        self.portfolio_risk_limit = 0.2  # Maximum percentage of capital at risk at any time
        self.correlation_threshold = 0.7  # Correlation threshold for diversification

        # Initialize notified buy and sell event IDs
        self.notified_buy_event_ids = set()
        self.notified_sell_event_ids = set()
        self.notified_rug_protection_tokens = set()  # CRITICAL FIX: Track rug protection notifications

        # PERMANENT FIX: Signal deduplication tracking
        self.processing_signals = set()
        self.last_processed_tokens = {}  # Track when tokens were last processed
        self.signal_lock = asyncio.Lock()

        # CRITICAL FIX: Clear rug protection tracking periodically (every hour)
        self._last_rug_protection_cleanup = time.time()

        # Memory management: Track cleanup intervals
        self._last_memory_cleanup = time.time()
        self._memory_cleanup_interval = 900  # 15 minutes (reduced from 1 hour)

        # SURGICAL FIX: Failed token blacklist to prevent infinite loops
        self.failed_tokens = {}  # {token_address: failure_timestamp}
        self.failed_token_cooldown = 300  # 5 minutes in seconds
        self.monitored_positions = set()  # Track actively monitored positions

    async def _ensure_unified_trader_initialized(self):
        """Ensure unified trader is properly initialized"""
        if not self._unified_trader_initialized:
            try:
                await self.unified_trader.initialize_traders()
                self._unified_trader_initialized = True
                logger.info("✅ Unified trader initialized successfully")

                # ENTERPRISE FIX: Synchronize network modes after initialization
                await self._sync_network_modes()

                # CRITICAL FIX: Validate trader initialization
                trader_info = self.unified_trader.get_current_trader_info()
                traders_available = trader_info.get('traders_available', {})

                if not traders_available.get('helius', False):
                    logger.warning("⚠️ Helius trader disabled - mainnet-only bot")
                if not traders_available.get('pumpportal', False):
                    logger.info("ℹ️ UnifiedTrader PumpPortal not available - using direct PumpPortal trader (this is normal)")

                logger.info(f"Trader status: Helius={traders_available.get('helius', False)}, PumpPortal={traders_available.get('pumpportal', False)}")

            except Exception as e:
                logger.error(f"❌ Failed to initialize unified trader: {e}")
                self._unified_trader_initialized = False

    async def _sync_network_modes(self):
        """Synchronize network modes - mainnet only"""
        try:
            if hasattr(self, 'unified_trader') and self.unified_trader:
                # Always use mainnet mode
                target_mode = self.network_mode  # Always MAINNET

                # Sync unified trader to match legacy state
                self.unified_trader.set_network_mode(target_mode)
                logger.debug(f"Network modes synchronized: {target_mode}")

        except Exception as e:
            logger.warning(f"Error synchronizing network modes: {e}")

    async def _validate_trader_initialization(self):
        """CRITICAL FIX: Validate that traders are properly initialized for selling"""
        try:
            # Check PumpPortal trader
            if not hasattr(self, 'pumpportal_trader') or not self.pumpportal_trader:
                logger.error("CRITICAL: PumpPortal trader not initialized - sells will fail")
                return False

            # Check unified trader
            if not hasattr(self, 'unified_trader') or not self.unified_trader:
                logger.error("CRITICAL: Unified trader not initialized - sells will fail")
                return False

            # Test trader functionality
            try:
                trader_info = self.unified_trader.get_current_trader_info()
                if not trader_info.get('supports_real_transactions', False):
                    logger.error("CRITICAL: Trader does not support real transactions - sells will fail")
                    return False

                logger.info(f"✅ Trader validation passed: {trader_info}")
                return True

            except Exception as e:
                logger.error(f"CRITICAL: Trader validation failed: {e}")
                return False

        except Exception as e:
            logger.error(f"CRITICAL: Trader validation error: {e}")
            return False

    async def _validate_sell_pipeline(self, token_address: str) -> bool:
        """CRITICAL FIX: Validate entire sell pipeline before execution"""
        try:
            # Check 1: Execution queue available
            if not hasattr(self, 'execution_queue') or not self.execution_queue:
                logger.error(f"CRITICAL: Execution queue not available for {token_address}")
                return False

            # Check 2: Trade executor available
            if not hasattr(self, 'trade_executor') or not self.trade_executor:
                logger.error(f"CRITICAL: Trade executor not available for {token_address}")
                return False

            # Check 3: Unified trader available
            if not hasattr(self, 'unified_trader') or not self.unified_trader:
                logger.error(f"CRITICAL: Unified trader not available for {token_address}")
                return False

            # Check 4: PumpPortal trader available
            if not hasattr(self.trade_executor, 'pumpportal_trader') or not self.trade_executor.pumpportal_trader:
                logger.error(f"CRITICAL: PumpPortal trader not available for {token_address}")
                return False

            # Check 5: Network mode is mainnet
            if self.network_mode != "MAINNET":
                logger.error(f"CRITICAL: Network mode is not MAINNET for {token_address}: {self.network_mode}")
                return False

            logger.info(f"✅ Sell pipeline validation passed for {token_address}")
            return True

        except Exception as e:
            logger.error(f"CRITICAL: Sell pipeline validation error for {token_address}: {e}")
            return False

    async def validate_unified_trader_integration(self) -> Dict[str, Any]:
        """ENTERPRISE VALIDATION: Comprehensive validation of unified trader integration"""
        validation_result = {
            "is_valid": True,
            "issues": [],
            "warnings": [],
            "trader_status": {},
            "balance_consistency": False,
            "network_sync": False
        }

        try:
            # Check unified trader initialization
            if not hasattr(self, 'unified_trader') or not self.unified_trader:
                validation_result["issues"].append("Unified trader not initialized")
                validation_result["is_valid"] = False
                return validation_result

            if not self._unified_trader_initialized:
                validation_result["warnings"].append("Unified trader not fully initialized")

            # Check trader availability
            trader_info = self.unified_trader.get_current_trader_info()
            validation_result["trader_status"] = trader_info

            # Validate balance consistency
            try:
                legacy_balance = await self.pumpportal_trader.get_balance() if self.pumpportal_trader else 0.0
                unified_balance = await self.unified_trader.get_balance()

                if abs(legacy_balance - unified_balance) < 0.0001:  # Allow for small floating point differences
                    validation_result["balance_consistency"] = True
                else:
                    validation_result["warnings"].append(f"Balance mismatch: Legacy={legacy_balance:.4f}, Unified={unified_balance:.4f}")

            except Exception as e:
                validation_result["warnings"].append(f"Balance validation error: {e}")

            # Check network mode synchronization
            try:
                expected_mode = self.network_mode  # Always MAINNET

                actual_mode = trader_info.get("network_mode", "UNKNOWN")
                if expected_mode == actual_mode:
                    validation_result["network_sync"] = True
                else:
                    validation_result["issues"].append(f"Network mode mismatch: Expected={expected_mode}, Actual={actual_mode}")
                    validation_result["is_valid"] = False

            except Exception as e:
                validation_result["issues"].append(f"Network sync validation error: {e}")
                validation_result["is_valid"] = False

            logger.info(f"Unified trader validation completed: {'✅ PASSED' if validation_result['is_valid'] else '❌ FAILED'}")

        except Exception as e:
            validation_result["issues"].append(f"Validation error: {e}")
            validation_result["is_valid"] = False
            logger.error(f"Error during unified trader validation: {e}")

        return validation_result

    def set_fast_analysis_function(self, fast_analysis_func):
        """Set the fast analysis function from main.py - FOR NEW SIGNALS ONLY"""
        self.fast_analysis_function = fast_analysis_func
        # Also set it in the analysis worker pool
        if hasattr(self, 'analysis_worker_pool') and self.analysis_worker_pool:
            self.analysis_worker_pool.set_fast_analysis_function(fast_analysis_func)
        logger.info("✅ Fast analysis function set in bot controller and worker pool (NEW SIGNALS ONLY)")

    def set_position_monitoring_function(self, position_monitoring_func):
        """Set the position monitoring function from main.py - FOR BOUGHT POSITIONS ONLY"""
        self.position_monitoring_function = position_monitoring_func

        # CRITICAL FIX: Ensure position monitoring starts even if function is None
        if not position_monitoring_func:
            logger.error("🚨 CRITICAL: Position monitoring function is None - AUTOMATIC SELLING WILL FAIL!")
            logger.error("🚨 CRITICAL: Bot will use fallback methods but may not sell positions automatically!")
        else:
            logger.info("✅ Position monitoring function properly set - automatic selling should work")
            logger.info("✅ Position monitoring function set in bot controller (BOUGHT POSITIONS ONLY)")

    async def analyze_new_signal_fast(self, token_address: str, **kwargs):
        """
        ANALYSIS PROCESS: Fast analysis for NEW signals only - NEVER for position monitoring
        Bulletproof separation with permanent skip tracking
        """
        if self.fast_analysis_function and callable(self.fast_analysis_function):
            logger.info(f"ANALYSIS PROCESS: Analyzing NEW signal {token_address}")
            return await self.fast_analysis_function(token_address, **kwargs)

        # No fallback - analysis function is required for NEW signals
        logger.error(f"ANALYSIS PROCESS: Fast analysis function not available for {token_address}")
        return {"exists": False, "reason": "Fast analysis function not available"}

    async def monitor_position_fast(self, token_address: str):
        """
        POSITION MONITORING: Lightweight monitoring for BOUGHT positions only
        NO full analysis, NO rug protection, NO signal processing
        """
        if self.position_monitoring_function and callable(self.position_monitoring_function):
            logger.debug(f"POSITION MONITORING: Checking {token_address}")
            return await self.position_monitoring_function(token_address)

        # Fallback to basic price check if monitoring function not available
        logger.warning(f"POSITION MONITORING: Monitoring function not available for {token_address}")
        return {"exists": False, "reason": "Position monitoring function not available"}

    async def initialize(self, enable_signal_processing=False):
        """Initialize bot controller without starting trading - PASSIVE INITIALIZATION ONLY."""
        if enable_signal_processing:
            logger.info("🚀 INITIALIZING BOT CONTROLLER (with signal processing)...")
            print("🚀 INITIALIZING BOT CONTROLLER (with signal processing)...")
        else:
            logger.info("🚀 INITIALIZING BOT CONTROLLER (passive mode - no signal processing)...")
            print("🚀 INITIALIZING BOT CONTROLLER (passive mode - no signal processing)...")

        try:
            # CRITICAL FIX: Initialize signal handler connection but control signal processing
            if enable_signal_processing:
                logger.info("🔗 Initializing signal handler WITH signal processing...")
                print("🔗 Initializing signal handler WITH signal processing...")
                await self.signal_handler.connect()
            else:
                logger.info("🔗 Initializing signal handler WITHOUT signal processing...")
                print("🔗 Initializing signal handler WITHOUT signal processing...")
                await self.signal_handler.connect(enable_processing=False)

            # CRITICAL FIX: Start signal listening immediately for signal detection
            logger.info("👂 Starting signal listening for token detection...")
            print("👂 Starting signal listening for token detection...")
            await self.signal_handler.start_listening()

            logger.info("✅ [SUCCESS] Signal handler initialized and listening for signals")
            print("✅ [SUCCESS] Signal handler initialized and listening for signals")

            # CRITICAL DEBUG: Check if analysis functions are set
            logger.info(f"🔍 CHECKING ANALYSIS FUNCTIONS:")
            print(f"🔍 CHECKING ANALYSIS FUNCTIONS:")
            logger.info(f"   - fast_analysis_function: {self.fast_analysis_function}")
            print(f"   - fast_analysis_function: {self.fast_analysis_function}")
            logger.info(f"   - position_monitoring_function: {self.position_monitoring_function}")
            print(f"   - position_monitoring_function: {self.position_monitoring_function}")

            if not self.fast_analysis_function:
                logger.error("🚫 CRITICAL ERROR: fast_analysis_function is NOT SET!")
                print("🚫 CRITICAL ERROR: fast_analysis_function is NOT SET!")
                logger.error("🚫 Bot will NOT be able to analyze signals!")
                print("🚫 Bot will NOT be able to analyze signals!")
            else:
                logger.info("✅ fast_analysis_function is properly set")
                print("✅ fast_analysis_function is properly set")

            if not self.position_monitoring_function:
                logger.error("🚫 CRITICAL ERROR: position_monitoring_function is NOT SET!")
                print("🚫 CRITICAL ERROR: position_monitoring_function is NOT SET!")
                logger.error("🚫 Bot will NOT be able to monitor positions!")
                print("🚫 Bot will NOT be able to monitor positions!")
            else:
                logger.info("✅ position_monitoring_function is properly set")
                print("✅ position_monitoring_function is properly set")

            # BULLETPROOF: Register signal callback immediately
            logger.info("🔗 Registering direct signal callback...")
            print("🔗 Registering direct signal callback...")
            self.signal_handler.register_direct_signal_callback(self.handle_new_signal)
            logger.info("✅ Direct signal callback registered successfully")
            print("✅ Direct signal callback registered successfully")

            # BULLETPROOF: Initialize analysis worker pool for signal processing
            logger.info("🔧 Initializing analysis worker pool...")
            print("🔧 Initializing analysis worker pool...")

            # Initialize token analyzer sessions first
            await self.token_analyzer.init_sessions()

            # Create analysis worker pool
            self.analysis_worker_pool = AnalysisWorkerPool(
                self.token_analyzer,
                num_workers=self.num_analysis_workers
            )

            # Set fast analysis function in worker pool if available
            if hasattr(self, 'fast_analysis_function') and self.fast_analysis_function:
                self.analysis_worker_pool.set_fast_analysis_function(self.fast_analysis_function)
                logger.info("✅ Fast analysis function set in worker pool")
                print("✅ Fast analysis function set in worker pool")

            # Start the worker pool
            await self.analysis_worker_pool.start()
            logger.info("✅ Analysis worker pool started successfully")
            print("✅ Analysis worker pool started successfully")

            # BULLETPROOF: Start signal processing loop immediately
            logger.info("🔄 Starting signal processing loop...")
            print("🔄 Starting signal processing loop...")
            self._signal_task = asyncio.create_task(self._process_signals_loop())
            logger.info("✅ Signal processing loop started")
            print("✅ Signal processing loop started")

        except Exception as e:
            logger.error(f"🚫 CRITICAL: Failed to initialize signal handler: {e}")
            print(f"🚫 CRITICAL: Failed to initialize signal handler: {e}")
            logger.error(f"🚫 Exception details: {traceback.format_exc()}")
            print(f"🚫 Exception details: {traceback.format_exc()}")
            raise

        # PERMANENT FIX: Start execution queue workers immediately to ensure sells always work
        # This is critical because sells can be triggered even when trading is "stopped"
        try:
            if not self.execution_queue.is_running:
                await self.execution_queue.start()
                logger.info("🚀 PERMANENT FIX: Execution queue workers started immediately for sell pipeline")
                print("🚀 PERMANENT FIX: Execution queue workers started immediately for sell pipeline")
            else:
                logger.info("✅ Execution queue workers already running")
        except Exception as e:
            logger.error(f"❌ CRITICAL: Failed to start execution queue workers: {e}")
            print(f"❌ CRITICAL: Failed to start execution queue workers: {e}")
            raise

        # SURGICAL FIX: Only initialize basic components, NO position monitoring
        # Position monitoring will start only when user explicitly starts trading
        logger.info("✅ [SUCCESS] Bot controller initialized in passive mode (ready for manual start)")
        print("✅ [SUCCESS] Bot controller initialized in passive mode (ready for manual start)")
        logger.info("💡 [INFO] Use CLI option 1 to start real trading")
        print("💡 [INFO] Use CLI option 1 to start real trading")

    async def _fallback_token_analysis(self, token_address: str, signal: Dict[str, Any]) -> bool:
        """
        Fallback token analysis when fast_analysis_function is not available.
        Uses basic token analyzer to determine if token should be traded.
        """
        try:
            logger.info(f"🔄 FALLBACK ANALYSIS: Starting basic analysis for {token_address}")

            # Use basic token analyzer
            if hasattr(self, 'token_analyzer') and self.token_analyzer:
                # Get basic token data
                token_data = await self.token_analyzer.get_token_data(token_address)

                if not token_data:
                    logger.warning(f"🚫 FALLBACK: No token data found for {token_address}")
                    return False

                # Basic checks
                market_cap = token_data.get('market_cap', 0)
                liquidity = token_data.get('liquidity', 0)

                logger.info(f"📊 FALLBACK DATA: MC=${market_cap:,.0f}, Liquidity=${liquidity:,.0f}")

                # Simple criteria (very basic)
                if market_cap > 1000 and liquidity > 1000:
                    logger.info(f"✅ FALLBACK: Basic criteria passed for {token_address}")
                    return True
                else:
                    logger.info(f"🚫 FALLBACK: Basic criteria failed for {token_address}")
                    return False
            else:
                logger.error(f"🚫 FALLBACK: No token analyzer available")
                return False

        except Exception as e:
            logger.error(f"🚫 FALLBACK ANALYSIS ERROR: {e}")
            return False

    async def _process_signal_analysis_only(self, token_address: str, signal: Dict[str, Any]):
        """
        Process signal for analysis only - no trading, just analysis and feedback.
        This ensures signals are always processed even when bot is not in trading mode.
        """
        try:
            logger.info(f"🔍 ANALYSIS-ONLY MODE: Processing {token_address}")
            print(f"🔍 ANALYSIS-ONLY MODE: Processing {token_address}")

            # Try fast analysis function first
            if self.fast_analysis_function:
                try:
                    logger.info(f"📊 Running fast analysis for {token_address}")
                    analysis_result = await self.fast_analysis_function(token_address, skip_notifications=True)

                    if analysis_result:
                        logger.info(f"✅ ANALYSIS COMPLETE: {token_address}")
                        logger.info(f"📊 Analysis result: {analysis_result}")
                        print(f"✅ ANALYSIS COMPLETE: {token_address}")

                        # Send analysis result as notification
                        if hasattr(self, 'signal_handler') and self.signal_handler:
                            try:
                                market_cap = analysis_result.get('market_cap', 0)
                                liquidity = analysis_result.get('liquidity_usd', 0)
                                price = analysis_result.get('price', 0)

                                analysis_msg = f"📊 [ANALYSIS COMPLETE] — {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                                analysis_msg += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                                analysis_msg += f"📦 Token: {token_address}\n"
                                analysis_msg += f"💰 Market Cap: ${market_cap:,.0f}\n"
                                analysis_msg += f"💧 Liquidity: ${liquidity:,.0f}\n"
                                analysis_msg += f"💲 Price: ${price:.8f}\n"
                                analysis_msg += f"✅ Analysis successful - token data retrieved"

                                asyncio.create_task(self.signal_handler.send_info_message(analysis_msg))
                            except Exception as notify_error:
                                logger.error(f"Error sending analysis notification: {notify_error}")
                    else:
                        logger.warning(f"⚠️ ANALYSIS FAILED: No data for {token_address}")
                        print(f"⚠️ ANALYSIS FAILED: No data for {token_address}")

                        # Send failure notification
                        if hasattr(self, 'signal_handler') and self.signal_handler:
                            try:
                                failure_msg = f"🛑 [ANALYSIS FAILED] — {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                                failure_msg += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                                failure_msg += f"📦 Token: {token_address}\n"
                                failure_msg += f"❌ No data available - token may not exist or have insufficient liquidity"

                                asyncio.create_task(self.signal_handler.send_info_message(failure_msg))
                            except Exception as notify_error:
                                logger.error(f"Error sending failure notification: {notify_error}")

                except Exception as analysis_error:
                    logger.error(f"🚫 Fast analysis error for {token_address}: {analysis_error}")
                    print(f"🚫 Fast analysis error for {token_address}: {analysis_error}")
            else:
                # Try fallback analysis
                logger.info(f"🔄 Using fallback analysis for {token_address}")
                fallback_result = await self._fallback_token_analysis(token_address, signal)
                if fallback_result:
                    logger.info(f"✅ FALLBACK ANALYSIS SUCCESSFUL: {token_address}")
                    print(f"✅ FALLBACK ANALYSIS SUCCESSFUL: {token_address}")
                else:
                    logger.warning(f"⚠️ FALLBACK ANALYSIS FAILED: {token_address}")
                    print(f"⚠️ FALLBACK ANALYSIS FAILED: {token_address}")

        except Exception as e:
            logger.error(f"🚫 Error in analysis-only processing: {e}")
            logger.error(f"🚫 Exception details: {traceback.format_exc()}")
            print(f"🚫 Error in analysis-only processing: {e}")

    async def _process_signal_direct_analysis(self, token_address: str, signal: Dict[str, Any]):
        """
        Process signal using direct analysis function (bypassing worker pool).
        This is used when worker pool is not available but fast analysis function is.
        """
        try:
            logger.info(f"🚀 DIRECT ANALYSIS: Processing {token_address}")
            print(f"🚀 DIRECT ANALYSIS: Processing {token_address}")

            if not self.fast_analysis_function:
                logger.error(f"🚫 No fast analysis function available for direct processing")
                return

            # Call fast analysis function directly
            logger.info(f"📊 Running direct fast analysis for {token_address}")
            print(f"📊 Running direct fast analysis for {token_address}")

            analysis_result = await self.fast_analysis_function(token_address, skip_notifications=True)

            if analysis_result:
                logger.info(f"✅ DIRECT ANALYSIS COMPLETE: {token_address}")
                logger.info(f"📊 Analysis result: {analysis_result}")
                print(f"✅ DIRECT ANALYSIS COMPLETE: {token_address}")

                # Send analysis result as notification
                if hasattr(self, 'signal_handler') and self.signal_handler:
                    try:
                        market_cap = analysis_result.get('market_cap', 0)
                        liquidity = analysis_result.get('liquidity_usd', 0)
                        price = analysis_result.get('price', 0)

                        analysis_msg = f"📊 [DIRECT ANALYSIS COMPLETE] — {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        analysis_msg += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                        analysis_msg += f"📦 Token: {token_address}\n"
                        analysis_msg += f"💰 Market Cap: ${market_cap:,.0f}\n"
                        analysis_msg += f"💧 Liquidity: ${liquidity:,.0f}\n"
                        analysis_msg += f"💲 Price: ${price:.8f}\n"
                        analysis_msg += f"✅ Direct analysis successful - token data retrieved"

                        asyncio.create_task(self.signal_handler.send_info_message(analysis_msg))
                        logger.info(f"📤 Analysis notification sent for {token_address}")
                        print(f"📤 Analysis notification sent for {token_address}")
                    except Exception as notify_error:
                        logger.error(f"Error sending analysis notification: {notify_error}")
            else:
                logger.warning(f"⚠️ DIRECT ANALYSIS FAILED: No data for {token_address}")
                print(f"⚠️ DIRECT ANALYSIS FAILED: No data for {token_address}")

                # Send failure notification
                if hasattr(self, 'signal_handler') and self.signal_handler:
                    try:
                        failure_msg = f"🛑 [DIRECT ANALYSIS FAILED] — {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        failure_msg += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                        failure_msg += f"📦 Token: {token_address}\n"
                        failure_msg += f"❌ No data available - token may not exist or have insufficient liquidity"

                        asyncio.create_task(self.signal_handler.send_info_message(failure_msg))
                    except Exception as notify_error:
                        logger.error(f"Error sending failure notification: {notify_error}")

        except Exception as e:
            logger.error(f"🚫 Error in direct analysis processing: {e}")
            logger.error(f"🚫 Exception details: {traceback.format_exc()}")
            print(f"🚫 Error in direct analysis processing: {e}")

    async def start_trading(self):
        """Start the trading process in real mode only."""
        if self.is_trading:
            print("Trading is already running.")
            return

        # Force real mode only
        self.run_mode = "REAL"
        print(f"Starting trading in {self.run_mode} mode...")
        self.status = "STARTING"
        self._stop_event.clear()
        self.is_trading = True

        # Set run mode in signal handler too
        self.signal_handler.run_mode = self.run_mode
        logger.info(f"Setting signal handler run mode to {self.run_mode}")

        try:
            # CRITICAL: Initialize unified trader first
            await self._ensure_unified_trader_initialized()

            # CRITICAL FIX: Validate trader initialization for selling
            trader_valid = await self._validate_trader_initialization()
            if not trader_valid:
                logger.error("❌ CRITICAL: Trader validation failed - sells will not work")
                raise Exception("Trader validation failed - cannot start trading")
            else:
                logger.info("✅ Trader validation passed - sells should work properly")

            # Initialize components that require async setup
            await self.token_analyzer.init_sessions()

            # Initialize parallel pipeline components
            self.analysis_worker_pool = AnalysisWorkerPool(
                self.token_analyzer,
                num_workers=self.num_analysis_workers
            )

            # Set fast analysis function in worker pool if available
            if hasattr(self, 'fast_analysis_function') and self.fast_analysis_function:
                self.analysis_worker_pool.set_fast_analysis_function(self.fast_analysis_function)
                logger.info("[SUCCESS] Fast analysis function set in newly created worker pool")

            await self.analysis_worker_pool.start()
            logger.info(f"Analysis worker pool started with {self.num_analysis_workers} workers")

            # PERMANENT FIX: Execution queue workers are now started immediately during initialization
            # This ensures sells work even when trading is "stopped"
            if not self.execution_queue.is_running:
                await self.execution_queue.start()
                logger.info(f"Execution queue started with {self.num_execution_workers} workers")
            else:
                logger.info("✅ Execution queue already running (started during initialization)")

            # Telegram connection is required for real trading mode
            telegram_connected = False

            # Maximum retries for Telegram connection
            max_retries = 3
            for retry in range(max_retries):
                try:
                    # No cooldown timer - always allow connection attempts
                    # Just log the attempt for debugging purposes
                    logger.info(f"Attempting to connect to Telegram (attempt {retry+1}/{max_retries})...")
                    print(f"Attempting to connect to Telegram (attempt {retry+1}/{max_retries})...")

                    # Connect to Telegram
                    logger.info("Connecting to Telegram (required for real trading)...")
                    print("Connecting to Telegram (required for real trading)...")

                    telegram_connected = await self.signal_handler.connect()

                    # Verify connection was successful
                    if not telegram_connected or not self.signal_handler.client:
                        if retry < max_retries - 1:
                            logger.warning(f"Telegram connection attempt {retry+1} failed, retrying...")
                            print(f"⚠️ Telegram connection attempt {retry+1} failed, retrying...")
                            await asyncio.sleep(2)  # Wait before retrying
                            continue
                        else:
                            error_msg = "Failed to connect to Telegram after multiple attempts. Real signals are required for trading."
                            logger.error(error_msg)
                            print(f"❌ {error_msg}")
                            raise Exception(error_msg)

                    # Check if client is connected
                    try:
                        # is_connected is a method, call it
                        if not self.signal_handler.client.is_connected():
                            if retry < max_retries - 1:
                                logger.warning(f"Telegram client not connected on attempt {retry+1}, retrying...")
                                print(f"⚠️ Telegram client not connected on attempt {retry+1}, retrying...")
                                await asyncio.sleep(2)  # Wait before retrying
                                continue
                            else:
                                raise Exception("Telegram client is not connected after multiple attempts.")
                    except Exception as e:
                        if retry < max_retries - 1:
                            logger.warning(f"Error checking Telegram connection on attempt {retry+1}: {e}")
                            print(f"⚠️ Error checking Telegram connection on attempt {retry+1}: {e}")
                            await asyncio.sleep(2)  # Wait before retrying
                            continue
                        else:
                            logger.error(f"Error checking Telegram connection: {e}")
                            raise Exception(f"Error checking Telegram connection: {e}")

                    # If we get here, connection was successful
                    break

                except Exception as e:
                    if retry < max_retries - 1:
                        logger.warning(f"Error during Telegram connection attempt {retry+1}: {e}")
                        print(f"⚠️ Error during Telegram connection attempt {retry+1}: {e}")
                        await asyncio.sleep(2)  # Wait before retrying
                        continue
                    else:
                        logger.error(f"Error during Telegram connection: {e}")
                        raise Exception(f"Error during Telegram connection: {e}")

            # GMGN trader has been replaced with PumpPortal trader
            # No additional initialization needed for PumpPortal trader
            if telegram_connected and self.signal_handler.client:
                logger.info("Telegram connected successfully - using PumpPortal trader for real mode execution")

            # Initialize other components
            try:
                # Connect WebSocket if needed
                try:
                    connected = await self.websocket_manager.connect()
                    if connected:
                        logger.info("WebSocket connected successfully")
                except Exception as e:
                    logger.warning(f"WebSocket connection failed: {e}")
                    print(f"WebSocket connection failed: {e}")
                    # Continue without WebSocket - it's not critical
            except Exception as e:
                logger.error(f"Error connecting to WebSocket: {e}")
                print(f"Warning: WebSocket connection failed: {e}")
                # Continue without WebSocket - it might not be critical

            # Start background tasks with improved error handling
            # CRITICAL: Start position monitoring first and independently
            try:
                logger.info("🔧 Starting position monitoring (critical component)...")
                await self._start_position_monitoring_resilient()
                logger.info("✅ Position monitoring started successfully")
            except Exception as monitor_error:
                logger.critical(f"❌ CRITICAL: Position monitoring failed to start: {monitor_error}")
                # Continue with other components but log critical error
                try:
                    error_msg = f"🚨 <b>CRITICAL STARTUP ERROR</b>\n\nPosition monitoring failed to start: {str(monitor_error)}\n\nTrading will continue but positions may not be monitored properly!"
                    if hasattr(self, 'signal_handler') and self.signal_handler:
                        asyncio.create_task(self.signal_handler.send_info_message(error_msg))
                except Exception:
                    pass

            # Start other background tasks
            try:
                # Start listening on Telegram with retries
                try:
                    # Telegram connection is required for real trading mode

                    # Try to start listening on Telegram with multiple retries
                    max_listen_retries = 3
                    listening_result = False

                    for listen_retry in range(max_listen_retries):
                        try:
                            logger.info(f"Starting Telegram listening (attempt {listen_retry+1}/{max_listen_retries})...")
                            print(f"Starting Telegram listening (attempt {listen_retry+1}/{max_listen_retries})...")

                            listening_result = await self.signal_handler.start_listening()

                            if listening_result:
                                logger.info(f"Successfully started Telegram listening on attempt {listen_retry+1}")
                                print(f"✅ Successfully started Telegram listening on attempt {listen_retry+1}")
                                break
                            else:
                                if listen_retry < max_listen_retries - 1:
                                    logger.warning(f"Failed to start Telegram listening on attempt {listen_retry+1}, retrying...")
                                    print(f"⚠️ Failed to start Telegram listening on attempt {listen_retry+1}, retrying...")
                                    await asyncio.sleep(2)  # Wait before retrying
                                else:
                                    # Last attempt failed
                                    error_msg = "Failed to start Telegram signal listening after multiple attempts. Real signals are required for trading."
                                    logger.error(error_msg)
                                    print(f"❌ {error_msg}")
                                    raise Exception(error_msg)
                        except Exception as listen_error:
                            if listen_retry < max_listen_retries - 1:
                                logger.warning(f"Error starting Telegram listening on attempt {listen_retry+1}: {listen_error}")
                                print(f"⚠️ Error starting Telegram listening on attempt {listen_retry+1}: {listen_error}")
                                await asyncio.sleep(2)  # Wait before retrying
                            else:
                                # Last attempt failed
                                raise listen_error

                    if listening_result:
                        self._signal_task = asyncio.create_task(self._process_signals_loop())
                        logger.info("Signal processing started successfully")

                        # Register direct signal processing callback for high-priority signals
                        if hasattr(self.signal_handler, 'register_direct_signal_callback'):
                            self.signal_handler.register_direct_signal_callback(self.handle_new_signal)
                            logger.info("Direct signal processing enabled for ALL channels (all channels are now priority)")
                            print("Direct signal processing enabled for ALL channels (all channels are now priority)")
                    else:
                        # This should not happen due to the retry loop above, but just in case
                        error_msg = "Failed to start Telegram signal listening. Real signals are required for trading."
                        logger.error(error_msg)
                        print(f"❌ {error_msg}")
                        raise Exception(error_msg)
                except Exception as e:
                    # Telegram listening is required for real trading mode
                    error_msg = f"Error starting Telegram signal listening: {e}"
                    logger.error(error_msg)
                    print(f"❌ {error_msg}")
                    self._signal_task = None
                    raise Exception(error_msg)

                # Position monitoring already started above
                pass
            except Exception as e:
                logger.error(f"Error starting background tasks: {e}")
                # Don't raise exception if position monitoring is already running
                # This allows the bot to continue even if other components fail
                logger.warning("Continuing startup despite background task errors...")

            self.status = "RUNNING"
            print(f"Trading bot started successfully in {self.run_mode} mode.")

            # Send startup message to Telegram (works in both real and simulation modes)
            # Use multiple attempts to ensure the message gets through
            max_startup_msg_attempts = 3
            startup_msg_sent = False

            for startup_attempt in range(max_startup_msg_attempts):
                try:
                    # Create a detailed startup message with emoji for better visibility
                    startup_msg = f"""🚀 Bot Started
🔄 Mode: {self.run_mode}
📊 Strategy: {self.current_strategy}
💰 Starting Capital: {self.state.starting_sol} SOL
🔢 Trade Size: {self.sol_per_trade} SOL
⏱️ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

                    # Try to send the message
                    send_result = await self.signal_handler.send_info_message(startup_msg)

                    if send_result:
                        # Message sent successfully
                        startup_msg_sent = True
                        # Remove emojis from log messages to avoid encoding errors in Windows console
                        clean_msg = startup_msg.replace('🚀', '').replace('📈', '').replace('💰', '').replace('🔄', '').replace('📊', '').replace('🔢', '').replace('⏱️', '')
                        logger.info(f"Sent startup message successfully on attempt {startup_attempt+1}: {clean_msg}")
                        print(f"✅ Sent startup message to Telegram successfully")
                        break
                    else:
                        # Message failed to send
                        logger.warning(f"Startup message send returned False on attempt {startup_attempt+1}")
                        if startup_attempt < max_startup_msg_attempts - 1:
                            logger.info(f"Retrying startup message in 2 seconds...")
                            await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"Error sending startup message (attempt {startup_attempt+1}): {e}")
                    if startup_attempt < max_startup_msg_attempts - 1:
                        logger.info(f"Retrying startup message in 2 seconds...")
                        await asyncio.sleep(2)

            # Log final status of startup message
            if not startup_msg_sent:
                logger.error(f"Failed to send startup message after {max_startup_msg_attempts} attempts")
                print(f"⚠️ Warning: Could not send startup message to Telegram after multiple attempts")
                # Continue without sending message - it's not critical

        except Exception as e:
            logger.error(f"Error starting trading: {e}", exc_info=True)
            print(f"Error starting trading: {e}")
            await self.stop_trading() # Attempt cleanup on error

    async def stop_trading(self):
        """Stop the trading process and cleanup resources."""
        if not self.is_trading and self.status != "STARTING":
            print("Trading is not running.")
            return

        print("Stopping trading...")
        self.status = "STOPPING"
        self._stop_event.set() # Signal loops to stop

        # Wait for tasks to finish with a timeout
        tasks = [self._signal_task, self._monitor_task] # Now include monitor task
        for task in tasks:
            if task and not task.done():
                try:
                    # Set a name for the task for better logging
                    if task == self._signal_task:
                        task_name = "signal_task"
                    elif task == self._monitor_task:
                        task_name = "monitor_task"
                    else:
                        task_name = "unknown_task"

                    logger.info(f"Waiting for {task_name} to finish (timeout: 5.0s)...")
                    await asyncio.wait_for(task, timeout=5.0)
                    logger.info(f"Task {task_name} finished gracefully.")
                except asyncio.TimeoutError:
                    logger.warning(f"Task {task_name} did not finish gracefully after 5.0s, cancelling.")
                    task.cancel()
                    try:
                        # Wait a bit for the task to be cancelled
                        await asyncio.wait_for(task, timeout=2.0)
                        logger.info(f"Task {task_name} cancelled successfully.")
                    except (asyncio.TimeoutError, asyncio.CancelledError):
                        logger.warning(f"Task {task_name} cancellation timed out or was already cancelled.")
                    except Exception as cancel_e:
                        logger.error(f"Error during {task_name} cancellation: {cancel_e}")
                except Exception as e:
                    logger.error(f"Error waiting for task {task_name}: {e}")

        await self.cleanup()
        self.is_trading = False
        self.status = "STOPPED"
        print("Trading stopped.")
        # Send shutdown message if Telegram is connected
        if self.signal_handler.client:
            try:
                # Use the send_info_message method which has proper error handling
                await self.signal_handler.send_info_message("Bot Stopped")
            except Exception as e:
                logger.error(f"Error sending shutdown message: {e}")
                print(f"Error sending shutdown message: {e}")

    # --- Added Signal Processing Logic ---
    async def _process_signals_loop(self):
        """Continuously process signals from the SignalHandler queue."""
        logger.info("Signal processing loop started")
        print("Signal processing loop started - waiting for signals...")

        try:
            queue_size = self.signal_handler.signal_queue.qsize()
            logger.info(f"Initial signal queue size: {queue_size}")
            print(f"Initial signal queue size: {queue_size}")
        except Exception as e:
            logger.error(f"Error checking initial queue size: {e}")

        while not self._stop_event.is_set():
            try:
                # CRITICAL FIX: Periodic cleanup of rug protection tracking (every hour)
                current_time = time.time()
                if current_time - self._last_rug_protection_cleanup > 3600:  # 1 hour
                    logger.info(f"Clearing rug protection notification tracking ({len(self.notified_rug_protection_tokens)} tokens)")
                    self.notified_rug_protection_tokens.clear()
                    self._last_rug_protection_cleanup = current_time

                # SURGICAL FIX: Non-blocking signal retrieval for ultra-fast processing
                raw_signal_from_handler = await self.signal_handler.get_signal() # Now non-blocking

                if raw_signal_from_handler:
                    token_address = raw_signal_from_handler.get('token_address')
                    source_type = raw_signal_from_handler.get('source_type', 'unknown_telegram')
                    channel_id = raw_signal_from_handler.get('channel_id', 'N/A')

                    logger.info(f"Received raw signal from queue for token: {token_address} from {source_type} (Channel: {channel_id})")
                    print(f"Processing raw signal from queue: {token_address} from {source_type}")

                    # Construct the signal object for handle_new_signal
                    signal_to_process = {
                        "token_address": token_address,
                        "token": token_address, # Retaining 'token' for compatibility
                        "source": source_type, # Use source_type as the general source identifier
                        "source_channel": channel_id, # Specific channel info
                        "raw_message": raw_signal_from_handler.get('raw_message', ''),
                        "text_metrics": raw_signal_from_handler.get('text_metrics', {}),
                        "timestamp": raw_signal_from_handler.get('timestamp', time.time()),
                        "is_gmgn_channel": raw_signal_from_handler.get('is_gmgn_channel', False)  # Include GMGN channel flag
                    }

                    await self.handle_new_signal(signal_to_process)
                else:
                    # SURGICAL FIX: Optimized polling for signal processing (5ms for better responsiveness)
                    await asyncio.sleep(0.005) # Faster polling to reduce signal queue buildup

            except asyncio.CancelledError:
                logger.info("Signal processing loop cancelled.")
                break
            except Exception as e:
                logger.error(f"Error in signal processing loop: {e}", exc_info=True)
                print(f"Error in signal processing loop: {e}")
                await asyncio.sleep(5) # Wait before retrying after error

    async def handle_new_signal(self, signal: Dict[str, Any]):
        """
        Handle a new signal with optimized processing for meme trading.

        This implementation includes:
        - Ultra-fast signal processing for volatile meme markets
        - Adaptive priority based on signal source and token characteristics
        - Optimized token address extraction with fallback mechanisms
        - Enhanced metadata for better analysis

        Args:
            signal: The signal data containing token address and metadata
        """
        signal_start_time = time.time() # For performance logging

        # CRITICAL DEBUG: Log signal reception with forced flush
        logger.info(f"🚨 SIGNAL RECEIVED: {signal}")
        print(f"🚨 SIGNAL RECEIVED: {signal}")

        # Get token address from either 'token_address' or 'token' field
        token_address = signal.get("token_address", signal.get("token"))
        if not token_address:
            logger.warning("Signal received without token address.")
            print("Signal received without token address.")
            return

        # CRITICAL DEBUG: Log token address extraction with forced flush
        logger.info(f"🎯 TOKEN ADDRESS EXTRACTED: {token_address}")
        print(f"🎯 TOKEN ADDRESS EXTRACTED: {token_address}")

        # BULLETPROOF: Force log flush to ensure logs are written immediately
        for handler in logger.handlers:
            handler.flush()

        # CRITICAL DEBUG: Check if fast analysis function is available
        if not self.fast_analysis_function:
            logger.error(f"🚫 CRITICAL ERROR: fast_analysis_function is None! Cannot analyze {token_address}")
            logger.error(f"🚫 ATTEMPTING FALLBACK ANALYSIS...")

            # BULLETPROOF FALLBACK: Use basic token analyzer if fast analysis fails
            try:
                if hasattr(self, 'token_analyzer') and self.token_analyzer:
                    logger.info(f"🔄 FALLBACK: Using basic token analyzer for {token_address}")
                    # Use basic analysis as fallback
                    analysis_result = await self._fallback_token_analysis(token_address, signal)
                    if analysis_result:
                        logger.info(f"✅ FALLBACK ANALYSIS SUCCESSFUL: {token_address}")
                        return
                    else:
                        logger.error(f"🚫 FALLBACK ANALYSIS FAILED: {token_address}")
                else:
                    logger.error(f"🚫 NO FALLBACK ANALYZER AVAILABLE: {token_address}")
            except Exception as e:
                logger.error(f"🚫 FALLBACK ANALYSIS ERROR: {e}")

            logger.error(f"🚫 SIGNAL WILL BE SKIPPED: {token_address}")
            return
        else:
            logger.info(f"✅ FAST ANALYSIS FUNCTION AVAILABLE: {self.fast_analysis_function}")

        # BULLETPROOF: Check if we can process signals (either worker pool OR direct function)
        can_process = False
        processing_method = ""

        if hasattr(self, 'analysis_worker_pool') and self.analysis_worker_pool:
            can_process = True
            processing_method = "worker_pool"
            logger.info(f"✅ ANALYSIS WORKER POOL AVAILABLE: {self.analysis_worker_pool}")
        elif self.fast_analysis_function:
            can_process = True
            processing_method = "direct_function"
            logger.info(f"✅ DIRECT ANALYSIS FUNCTION AVAILABLE: {self.fast_analysis_function}")
        else:
            logger.error(f"🚫 CRITICAL ERROR: No analysis method available! Cannot analyze {token_address}")
            logger.error(f"🚫 SIGNAL WILL BE SKIPPED: {token_address}")
            return

        logger.info(f"📊 PROCESSING METHOD: {processing_method}")

        # CRITICAL FIX: Enhanced signal deduplication with timestamp check
        current_time = time.time()
        async with self.signal_lock:
            # Check if token is currently being processed
            if token_address in self.processing_signals:
                logger.info(f"Signal for {token_address} already being processed, skipping duplicate")
                return

            # Check if we processed this token very recently (within 5 seconds)
            last_processed_time = self.last_processed_tokens.get(token_address, 0)
            if current_time - last_processed_time < 5.0:
                logger.info(f"Token {token_address} processed {current_time - last_processed_time:.1f}s ago, skipping duplicate")
                return

            # Mark as being processed and update timestamp
            self.processing_signals.add(token_address)
            self.last_processed_tokens[token_address] = current_time

            # SURGICAL FIX: Send "analysis in progress" message ONLY after confirming processing
            # This prevents race condition with rejection messages
            try:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                source = signal.get('source', 'unknown')
                confidence = signal.get('confidence', 0.5)

                analysis_msg = f"""🚨 [SIGNAL ALERT] — {timestamp}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📦 Token CA: {token_address}
📡 Source: {source.upper()}
📊 Confidence Score: {confidence:.2f}

⚠️ Token under fast evaluation...
➡️ Analysis in progress - expect update within 5 seconds.
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"""

                # Send analysis in progress notification
                if hasattr(self, 'signal_handler') and self.signal_handler:
                    asyncio.create_task(self.signal_handler.send_info_message(analysis_msg))

            except Exception as e:
                logger.error(f"Error sending analysis in progress message: {e}")

            # CRITICAL FIX: Ensure we handle tokens with 'pump' suffix correctly
            if token_address.lower().endswith("pump"):
                logger.info(f"Detected token with 'pump' suffix: {token_address}")
                # No need to modify the address - just log it for visibility

            logger.info(f"Processing signal for token: {token_address} from source: {signal.get('source', 'unknown')}")
            print(f"🔔 New Signal Detected:\nToken: {token_address}\nSource: {signal.get('source', 'basic')}") # Keep a simple console print for new signals

            # BULLETPROOF FIX: Always process signals for analysis, regardless of trading mode
            if not self.is_trading:
                logger.info(f"🔍 Bot not in trading mode, processing signal for ANALYSIS ONLY: {token_address}")
                # Process signal for analysis only - no trading checks needed
                if processing_method == "direct_function":
                    await self._process_signal_direct_analysis(token_address, signal)
                else:
                    await self._process_signal_analysis_only(token_address, signal)
                return
            else:
                logger.info(f"💰 Bot in trading mode, processing signal for TRADING: {token_address}")

            if not self.state.can_trade(token_address, self.cooldown_seconds):
                logger.info(f"Token {token_address} is on cooldown or already traded. Skipping.")
                return

            open_positions = self.state.get_open_positions()
            if len(open_positions) >= self.max_concurrent_trades:
                logger.info(f"Max concurrent trades ({self.max_concurrent_trades}) reached. Skipping {token_address}.")
                return

        # Define the callback for when analysis is complete
        async def analysis_callback(analysis_result_data: Dict[str, Any]):
            # SURGICAL FIX: Handle cached results
            if analysis_result_data.get("cached", False) or analysis_result_data.get("skip", False):
                logger.debug(f"Analysis skipped due to cache: {analysis_result_data.get('reason')}")
                return  # Exit early for cached results

            analysis_time = time.time() - signal_start_time
            logger.info(f"Analysis completed for {token_address} in {analysis_time:.3f}s. Result: {analysis_result_data}")

            # SURGICAL FIX: Cache analysis result for deduplication
            try:
                reason = analysis_result_data.get('reason', 'analyzed')
                cache_token_analysis(token_address, "analyzed", reason)
                logger.debug(f"Cached analysis result for {token_address}: {reason}")
            except Exception as e:
                logger.warning(f"Failed to cache analysis result for {token_address}: {e}")

            # CRITICAL FIX: Check if this is position monitoring (should NOT trigger buy)
            # Check both signal_metadata AND analysis_result_data for monitoring flags
            is_position_monitoring = (
                signal_metadata.get('real_time_monitoring', False) or
                signal_metadata.get('skip_notifications', False) or
                signal_metadata.get('force_fresh', False) or
                analysis_result_data.get('skip_notifications', False) or
                analysis_result_data.get('real_time_monitoring', False) or
                analysis_result_data.get('force_fresh', False)
            )
            is_active_position_analysis = (
                signal_metadata.get('is_active_position', False) or
                analysis_result_data.get('is_active_position', False)
            )

            if is_position_monitoring or (is_active_position_analysis and self.state.has_position(token_address)):
                logger.info(f"POSITION MONITORING: Skipping buy execution for {token_address} (monitoring existing position)")
                return

            if not analysis_result_data or not analysis_result_data.get("exists", False):
                logger.info(f"Token {token_address} analysis failed or token doesn't exist on DexScreener.")
                # Log extracted metrics if available, even if token not found
                extracted_metrics = analysis_result_data.get('extracted_metrics', {})
                metrics_info_parts = []
                if extracted_metrics:
                    if extracted_metrics.get('token_name'):
                        metrics_info_parts.append(f"Token Name: ${extracted_metrics.get('token_name')}")
                    if extracted_metrics.get('volume'):
                         metrics_info_parts.append(f"Signal Volume: ${extracted_metrics.get('volume')}")
                    # Add other relevant extracted metrics if needed

                metrics_info_str = "\\n".join(metrics_info_parts)
                if metrics_info_str:
                    metrics_info_str = f"\\nSignal Metrics:\\n{metrics_info_str}"

                # Get current timestamp
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # SURGICAL FIX: Accurate error message mapping based on actual failure reason
                reason = analysis_result_data.get('reason', '') if analysis_result_data else ''
                market_cap = analysis_result_data.get('market_cap', 0) if analysis_result_data else 0
                liquidity_usd = analysis_result_data.get('liquidity_usd', 0) if analysis_result_data else 0
                rug_risk = analysis_result_data.get('rug_risk', '') if analysis_result_data else ''

                # Determine accurate rejection reason and message title
                if 'liquidity' in reason.lower() or 'LOW_LIQUIDITY' in rug_risk:
                    message_title = "🛑 [TRADE SKIPPED – LOW LIQUIDITY]"
                    rejection_reasons = f" • Liquidity too low (${liquidity_usd:,.0f} < ${self.min_liquidity_usd:,.0f})\n • High rug risk detected\n • Failed rug protection checks"
                elif 'market_cap' in reason.lower() or 'LOW_MARKET_CAP' in rug_risk:
                    message_title = "🛑 [TRADE SKIPPED – LOW MARKET CAP]"
                    rejection_reasons = f" • Market cap too low (${market_cap:,.0f})\n • High rug risk detected\n • Failed rug protection checks"
                elif 'whale' in reason.lower() or 'HIGH_WHALE_RISK' in rug_risk:
                    message_title = "🛑 [TRADE SKIPPED – HIGH WHALE RISK]"
                    rejection_reasons = f" • High whale concentration detected\n • Rug risk from large holders\n • Failed rug protection checks"
                elif 'blacklist' in reason.lower() or 'BLACKLISTED' in rug_risk:
                    message_title = "🛑 [TRADE SKIPPED – BLACKLISTED TOKEN]"
                    rejection_reasons = f" • Token is blacklisted\n • Known scam/rug detected\n • Failed rug protection checks"
                else:
                    # Fallback to original message for genuine API failures
                    message_title = "🛑 [TRADE SKIPPED – TOKEN NOT FOUND]"
                    rejection_reasons = f" • Token not found on DexScreener\n • No liquidity data available\n • Unable to analyze token metrics"

                # Format the notification with accurate information
                not_found_notification = (
                    f"{message_title} — {timestamp}\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    f"📦 Token: {token_address}\n"
                    f"💰 Market Cap: ${market_cap:,.0f}\n"
                    f"💧 Liquidity: ${liquidity_usd:,.0f}\n\n"
                    f"❌ Reasons for skipping:\n"
                    f"{rejection_reasons}{metrics_info_str}\n\n"
                    f"✅ Action: Trade skipped to reduce risk."
                )
                # Check if we should skip notifications (from position monitoring)
                skip_notifications = analysis_result_data.get('skip_notifications', False)

                if skip_notifications:
                    logger.info(f"Skipping token not found notification for {token_address} as requested by skip_notifications flag")
                else:
                    try:
                        await self.signal_handler.send_info_message(not_found_notification)
                    except Exception as e_notify:
                        logger.error(f"Error sending token not found notification: {e_notify}")
                return

            # Successful analysis, proceed with trade decision
            confidence = analysis_result_data.get("confidence", 0.0)
            price = analysis_result_data.get("price", 0.0)
            reason = analysis_result_data.get("reason", "")
            liquidity = analysis_result_data.get("liquidity_usd", 0.0)
            volume_5m = analysis_result_data.get("volume_5m", 0.0) # Make sure 'volume_5m' is consistently provided by TokenAnalyzer

            logger.info(f"Analyzed {token_address}: Price=${price:.8f}, Initial Confidence={confidence:.2f}, Liquidity=${liquidity:,.2f}, 5m Vol=${volume_5m:,.2f}, Reason='{reason}'")
            print(f"📊 Analysis for {token_address}: Price=${price:.8f}, Confidence={confidence:.2f}, Liquidity=${liquidity:,.0f}")


            # Technical analysis removed - using fundamental analysis only
            tech_score = 0.5  # Default neutral score for notification display


            # Use the strategy's confidence threshold or fallback to a default from settings.
            min_conf_for_trade = self.min_confidence # Default from init

            logger.info(f"Final confidence for {token_address}: {confidence:.2f} (Threshold: {min_conf_for_trade:.2f})")

            if confidence < min_conf_for_trade:
                logger.info(f"Confidence {confidence:.2f} for {token_address} is below threshold {min_conf_for_trade:.2f}. Skipping trade.")

                # Get additional metrics for the notification
                volume_24h = analysis_result_data.get('volume_24h', 0)
                volume_5m = analysis_result_data.get('volume_5m', 0)
                market_cap = analysis_result_data.get('market_cap', analysis_result_data.get('market_cap_usd', 0))

                # CRITICAL FIX: Extract liquidity data - this was missing and causing $0 liquidity!
                liquidity = analysis_result_data.get('liquidity_usd', analysis_result_data.get('liquidity', 0))

                # Ensure numeric values for formatting
                try:
                    volume_24h = float(volume_24h) if volume_24h else 0
                    market_cap = float(market_cap) if market_cap else 0

                    # CRITICAL FIX: Handle liquidity which might be a formatted string like "$45,000"
                    if isinstance(liquidity, str):
                        liquidity = float(liquidity.replace('$', '').replace(',', ''))
                    else:
                        liquidity = float(liquidity) if liquidity else 0
                except (ValueError, TypeError):
                    logger.warning(f"Error converting metrics to float for {token_address}")
                    volume_24h = market_cap = liquidity = 0

                # Determine specific reasons for low confidence
                reasons = []
                if liquidity < 8000:  # User requested 8000 threshold
                    reasons.append(f"Low liquidity (${liquidity:,.0f} < $8,000)")
                if volume_24h < 10000:  # User requested 10000 threshold
                    reasons.append(f"Low 24h volume (${volume_24h:,.0f} < $10,000)")
                if market_cap < 15000:  # User requested 15000 threshold
                    reasons.append(f"Low market cap (${market_cap:,.0f} < $15,000)")
                if tech_score < 0.5:
                    reasons.append(f"Poor technical indicators (Score: {tech_score:.2f})")

                # If no specific reasons found, use generic reason
                if not reasons:
                    reasons.append("Confidence too low after analysis")

                # Format reasons as bullet points
                reasons_str = "\n".join([f" • {reason}" for reason in reasons])

                # Send low confidence notification with the new format
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                low_conf_notification = (
                    f"🛑 [TRADE SKIPPED – LOW CONFIDENCE] — {timestamp}\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    f"📦 Token: {token_address}\n"
                    f"📉 Confidence: {confidence:.2f} (Threshold: {min_conf_for_trade:.2f})\n"
                    f"💧 Liquidity: ${liquidity:,.0f}\n"
                    f"💵 Price: ${price:.8f}\n"
                    f"📊 Volume (24h): ${volume_24h:,.0f}\n"
                    f"💰 Market Cap: ${market_cap:,.0f}\n\n"
                    f"❌ Reasons for skipping:\n{reasons_str}\n\n"
                    f"✅ Action: Trade skipped to reduce risk."
                )
                # Check if we should skip notifications (from position monitoring)
                skip_notifications = analysis_result_data.get('skip_notifications', False)

                if skip_notifications:
                    logger.info(f"Skipping low confidence notification for {token_address} as requested by skip_notifications flag")
                else:
                    try:
                        await self.signal_handler.send_info_message(low_conf_notification)
                    except Exception as e_notify_low_conf:
                        logger.error(f"Error sending low confidence notification: {e_notify_low_conf}")
                return

            # CRITICAL FIX: Check rug protection reason BEFORE proceeding with buy
            if reason and any(rug_keyword in reason.lower() for rug_keyword in [
                'rug risk', 'low market cap', 'low liquidity', 'low volume', 'insufficient age',
                'high holder concentration', 'blacklist', 'dev wallet'
            ]):
                logger.warning(f"RUG PROTECTION TRIGGERED for {token_address}: {reason}")

                # Get additional metrics for the notification
                volume_24h = analysis_result_data.get('volume_24h', 0)
                volume_5m = analysis_result_data.get('volume_5m', 0)
                market_cap = analysis_result_data.get('market_cap', analysis_result_data.get('market_cap_usd', 0))
                liquidity = analysis_result_data.get('liquidity_usd', analysis_result_data.get('liquidity', 0))

                # Get rug protection specific metrics
                token_age = analysis_result_data.get('token_age', 'Unknown')
                fdv = analysis_result_data.get('fdv', 0)
                holder_count = analysis_result_data.get('holder_count', 0)
                top10_pct = analysis_result_data.get('top10_pct', 0)
                is_blacklisted = analysis_result_data.get('is_blacklisted', False)
                pair_age_minutes = analysis_result_data.get('pair_age_minutes', 0)

                rug_protection_notification = f"""🛡️ [RUG PROTECTION ALERT] 🚫 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🚨 Token BLOCKED by Rug Protection
📍 Token CA: {token_address[:20]}...
🏷️ Symbol: {analysis_result_data.get('symbol', 'Unknown')}
🔍 Source: {signal.get('source', 'unknown')}

🛡️ Protection Reason:
{reason}

📈 Token Metrics:
 • Market Cap: ${market_cap:,.0f}
 • Liquidity: ${liquidity:,.0f}
 • 5m Volume: ${volume_5m:,.0f}
 • 24h Volume: ${volume_24h:,.0f}
 • Confidence: {confidence:.3f}

🛡️ Rug Protection Details:
 • Age: {token_age} ({pair_age_minutes:.1f} minutes)
 • FDV: ${fdv:,.0f}
 • Holders: {holder_count:,}
 • Top 10 Holdings: {top10_pct:.1f}%
 • Blacklisted: {'❌ YES' if is_blacklisted else '✅ NO'}

⚠️ This token failed rug protection checks and was NOT purchased.
✅ Your capital is protected from potential scams."""

                # Check if we should skip notifications
                skip_notifications = analysis_result_data.get('skip_notifications', False)

                # CRITICAL FIX: Prevent duplicate rug protection notifications
                if token_address in self.notified_rug_protection_tokens:
                    logger.info(f"Rug protection notification already sent for {token_address}, skipping duplicate")
                elif skip_notifications:
                    logger.info(f"Skipping rug protection notification for {token_address} as requested by skip_notifications flag")
                else:
                    try:
                        await self.signal_handler.send_info_message(rug_protection_notification)
                        self.notified_rug_protection_tokens.add(token_address)  # Track notification sent
                        logger.info(f"Rug protection notification sent for {token_address}")
                    except Exception as e_notify_rug:
                        logger.error(f"Error sending rug protection notification: {e_notify_rug}")
                return

            # Additional safety checks
            if price <= 1e-18: # Effectively zero or negative
                logger.warning(f"Invalid price ({price}) for {token_address}. Skipping buy.")

                # Get additional metrics for the notification
                volume_24h = analysis_result_data.get('volume_24h', 0)
                liquidity = analysis_result_data.get('liquidity_usd', 0)
                market_cap = analysis_result_data.get('market_cap', analysis_result_data.get('market_cap_usd', 0))

                # CRITICAL FIX: Ensure numeric values for formatting, handle formatted strings
                try:
                    # Handle volume_24h which might be a formatted string like "$42,153"
                    if isinstance(volume_24h, str):
                        volume_24h = float(volume_24h.replace('$', '').replace(',', ''))
                    else:
                        volume_24h = float(volume_24h) if volume_24h else 0

                    liquidity = float(liquidity) if liquidity else 0
                    market_cap = float(market_cap) if market_cap else 0
                except (ValueError, TypeError):
                    logger.warning(f"Error converting metrics to float for {token_address}")
                    volume_24h = liquidity = market_cap = 0

                # Send invalid price notification with the new format
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                invalid_price_notification = (
                    f"🛑 [TRADE SKIPPED – INVALID PRICE] — {timestamp}\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    f"📦 Token: {token_address}\n"
                    f"📉 Confidence: {confidence:.2f} (Threshold: {min_conf_for_trade:.2f})\n"
                    f"💧 Liquidity: ${liquidity:,.0f}\n"
                    f"💵 Price: ${price:.8f}\n"
                    f"📊 Volume (24h): ${volume_24h:,.0f}\n"
                    f"💰 Market Cap: ${market_cap:,.0f}\n\n"
                    f"❌ Reasons for skipping:\n"
                    f" • Invalid or zero price detected\n\n"
                    f"✅ Action: Trade skipped to reduce risk."
                )

                # Check if we should skip notifications
                skip_notifications = analysis_result_data.get('skip_notifications', False)

                if skip_notifications:
                    logger.info(f"Skipping invalid price notification for {token_address} as requested by skip_notifications flag")
                else:
                    try:
                        await self.signal_handler.send_info_message(invalid_price_notification)
                    except Exception as e_notify_invalid_price:
                        logger.error(f"Error sending invalid price notification: {e_notify_invalid_price}")
                return

            # REMOVED: Zero liquidity blocking logic that was rejecting profitable trades
            # DexScreener API now provides accurate liquidity readings

            min_liquidity = self.trade_settings.get('min_liquidity_usd', 1000)
            if liquidity < min_liquidity:
                logger.warning(f"Insufficient liquidity (${liquidity:,.2f} < ${min_liquidity:,.2f}) for {token_address}. Skipping buy.")

                # Get additional metrics for the notification
                volume_24h = analysis_result_data.get('volume_24h', 0)
                market_cap = analysis_result_data.get('market_cap', analysis_result_data.get('market_cap_usd', 0))

                # CRITICAL FIX: Ensure numeric values for formatting, handle formatted strings
                try:
                    # Handle volume_24h which might be a formatted string like "$42,153"
                    if isinstance(volume_24h, str):
                        volume_24h = float(volume_24h.replace('$', '').replace(',', ''))
                    else:
                        volume_24h = float(volume_24h) if volume_24h else 0

                    market_cap = float(market_cap) if market_cap else 0
                except (ValueError, TypeError):
                    logger.warning(f"Error converting metrics to float for {token_address}")
                    volume_24h = market_cap = 0

                # Send low liquidity notification with the new format
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                low_liquidity_notification = (
                    f"🛑 [TRADE SKIPPED – LOW LIQUIDITY] — {timestamp}\n"
                    f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                    f"📦 Token: {token_address}\n"
                    f"📉 Confidence: {confidence:.2f} (Threshold: {min_conf_for_trade:.2f})\n"
                    f"💧 Liquidity: ${liquidity:,.0f}\n"
                    f"💵 Price: ${price:.8f}\n"
                    f"📊 Volume (24h): ${volume_24h:,.0f}\n"
                    f"💰 Market Cap: ${market_cap:,.0f}\n\n"
                    f"❌ Reasons for skipping:\n"
                    f" • Insufficient liquidity (${liquidity:,.0f} < ${min_liquidity:,.0f})\n\n"
                    f"✅ Action: Trade skipped to reduce risk."
                )

                # Check if we should skip notifications
                skip_notifications = analysis_result_data.get('skip_notifications', False)

                if skip_notifications:
                    logger.info(f"Skipping low liquidity notification for {token_address} as requested by skip_notifications flag")
                else:
                    try:
                        await self.signal_handler.send_info_message(low_liquidity_notification)
                    except Exception as e_notify_low_liq:
                        logger.error(f"Error sending low liquidity notification: {e_notify_low_liq}")
                return

            # --- Execute Buy with Dynamic Position Sizing ---
            # CRITICAL FIX: Always use self.sol_per_trade (user's CLI setting) instead of strategy value
            base_sol_amount = self.sol_per_trade  # Always use the user's CLI setting

            # Log the source of the SOL amount for debugging
            logger.info(f"Using SOL per trade amount: {base_sol_amount} SOL (from user CLI setting)")

            # SURGICAL FIX: Check available balance before position sizing
            available_sol = self.state.available_sol
            logger.info(f"Available balance: {available_sol:.4f} SOL")

            # Use fixed position size without confidence-based adjustments
            # Only apply portfolio risk factor for overall risk management
            portfolio_risk_factor = self._calculate_portfolio_risk_factor() # Assumes this method exists and is sound
            buy_amount_sol = base_sol_amount * portfolio_risk_factor

            # Check if we have user-set position sizes in trading_settings
            # These would have been set by set_sol_per_trade_interactive
            if hasattr(self, 'trading_settings') and 'min_position_size_sol' in self.trading_settings and 'max_position_size_sol' in self.trading_settings:
                # Use the values from trading_settings (user input takes priority)
                min_pos_size = self.trading_settings.get('min_position_size_sol')
                max_pos_size = self.trading_settings.get('max_position_size_sol')
                logger.info(f"Using position size from user input: min={min_pos_size}, max={max_pos_size}")
            else:
                # Fall back to default values based on base amount
                min_pos_size = base_sol_amount * self.min_position_size_percent
                max_pos_size = base_sol_amount * self.max_position_size_percent
                logger.info(f"Using default position size: min={min_pos_size}, max={max_pos_size}")

            # If min and max are the same (user set a fixed amount), use that exact amount
            if min_pos_size == max_pos_size:
                buy_amount_sol = min_pos_size
                logger.info(f"Using fixed position size: {buy_amount_sol} SOL (user-defined)")
            else:
                # Otherwise apply min/max constraints
                buy_amount_sol = max(min_pos_size, min(buy_amount_sol, max_pos_size))


            # Get available SOL with fallback to direct attribute access
            try:
                available_sol = self.state.get_available_sol()
            except AttributeError:
                # Fallback to direct attribute access if method doesn't exist
                available_sol = getattr(self.state, 'available_sol', 0.0)
                logger.warning(f"Using fallback method to get available SOL: {available_sol:.4f}")

            # SURGICAL FIX: Enhanced balance validation with fee calculation
            # Get external fees to calculate total cost
            external_fees = self.config.get_external_fees()
            estimated_fees = (
                external_fees.get('buy_tip_sol', 0.0001) +  # Fixed: 0.1 milliSOL not 5 milliSOL
                external_fees.get('gas_price_sol', 0.0001) +  # Fixed: 0.1 milliSOL not 5 milliSOL
                (buy_amount_sol * external_fees.get('handling_fee_percent', 1.0) / 100) +
                (buy_amount_sol * external_fees.get('platform_fee_percent', 1.0) / 100)
            )

            total_cost_sol = buy_amount_sol + estimated_fees

            # Check if we have enough available SOL for this trade including fees
            if total_cost_sol > available_sol:
                if available_sol < 0.05:  # Minimum threshold for any trade
                    logger.warning(f"Insufficient balance for any trade: {available_sol:.4f} SOL available, minimum 0.05 SOL required")
                    await self.signal_handler.send_info_message(
                        f"⚠️ [TRADE SKIPPED - INSUFFICIENT BALANCE]\n"
                        f"📦 Token: {token_address}\n"
                        f"💰 Available: {available_sol:.4f} SOL\n"
                        f"💸 Required: {total_cost_sol:.4f} SOL (position: {buy_amount_sol:.4f} + fees: {estimated_fees:.4f})\n"
                        f"🔄 Consider reducing position size or adding more SOL"
                    )
                    return

                # SURGICAL FIX: Dynamic position sizing when insufficient funds
                # Calculate maximum affordable position size
                max_available = available_sol - 0.01  # Reserve 0.01 SOL buffer for safety

                # Solve for position size: position_size + fees = max_available
                # fees = fixed_fees + (position_size * variable_fee_percent)
                fixed_fees = external_fees.get('buy_tip_sol', 0.005) + external_fees.get('gas_price_sol', 0.005)
                variable_fee_percent = (external_fees.get('handling_fee_percent', 1.0) + external_fees.get('platform_fee_percent', 1.0)) / 100

                # position_size * (1 + variable_fee_percent) + fixed_fees = max_available
                # position_size = (max_available - fixed_fees) / (1 + variable_fee_percent)
                adjusted_position_size = (max_available - fixed_fees) / (1 + variable_fee_percent)

                if adjusted_position_size < 0.01:  # Too small to be viable
                    logger.warning(f"Calculated position size too small: {adjusted_position_size:.4f} SOL")
                    await self.signal_handler.send_info_message(
                        f"⚠️ [TRADE SKIPPED - POSITION TOO SMALL]\n"
                        f"📦 Token: {token_address}\n"
                        f"💰 Available: {available_sol:.4f} SOL\n"
                        f"📏 Calculated position: {adjusted_position_size:.4f} SOL (too small)\n"
                        f"🔄 Add more SOL to enable trading"
                    )
                    return

                # Use adjusted position size
                original_amount = buy_amount_sol
                buy_amount_sol = adjusted_position_size

                # Recalculate fees for adjusted position
                estimated_fees = (
                    external_fees.get('buy_tip_sol', 0.0001) +  # Fixed: 0.1 milliSOL not 5 milliSOL
                    external_fees.get('gas_price_sol', 0.0001) +  # Fixed: 0.1 milliSOL not 5 milliSOL
                    (buy_amount_sol * external_fees.get('handling_fee_percent', 1.0) / 100) +
                    (buy_amount_sol * external_fees.get('platform_fee_percent', 1.0) / 100)
                )
                total_cost_sol = buy_amount_sol + estimated_fees

                logger.info(f"DYNAMIC POSITION SIZING: Reduced from {original_amount:.4f} to {buy_amount_sol:.4f} SOL (available: {available_sol:.4f}, total cost: {total_cost_sol:.4f})")

                await self.signal_handler.send_info_message(
                    f"⚠️ [POSITION SIZE ADJUSTED]\n"
                    f"📦 Token: {token_address}\n"
                    f"📏 Reduced position: {original_amount:.4f} → {buy_amount_sol:.4f} SOL\n"
                    f"💰 Available: {available_sol:.4f} SOL\n"
                    f"💸 Total cost: {total_cost_sol:.4f} SOL"
                )

            if buy_amount_sol <= 1e-5: # Minimum practical buy amount for SOL
                logger.warning(f"Calculated buy amount {buy_amount_sol:.4f} SOL for {token_address} is too low. Skipping.")
                return

            # Real trading only - simulation mode removed
            logger.info(f"Position sizing for {token_address}: Base={base_sol_amount:.4f}, PortRiskFactor={portfolio_risk_factor:.2f}, Final={buy_amount_sol:.4f} SOL. Mode: {self.run_mode}")

            buy_order_result_future = asyncio.Future()
            async def buy_execution_callback(exec_result: Dict[str, Any]):
                buy_order_result_future.set_result(exec_result)

            # Priority for buy order (lower number is higher priority)
            # Use fixed priority based on strategy criteria only
            buy_priority = 5  # Fixed priority for all buy orders

            signal_unique_part = signal.get('id', signal.get('message_id', str(time.time()))) # Ensure unique enough
            event_id = f"buy_{token_address}_{signal_unique_part}"
            logger.info(f"Generated buy event_id: {event_id} for token {token_address}")

            # Use strategy-specific slippage from finalconfig.json with adaptive support
            # The adaptive slippage logic is now handled inside get_strategy_slippage()
            strategy_slippage = self.get_strategy_slippage(token_address, analysis_result_data)
            current_slippage_bps = int(strategy_slippage * 10000)

            # DIAGNOSTIC: Enhanced slippage logging
            logger.info(f"🎯 SLIPPAGE CALCULATION for {token_address}:")
            logger.info(f"   Strategy: {self.current_strategy}")
            logger.info(f"   Final Slippage: {strategy_slippage:.4f} ({current_slippage_bps} bps)")
            logger.info(f"   Market Data: Liq=${analysis_result_data.get('liquidity_usd', 0):,.0f}, Vol=${analysis_result_data.get('volume_5m', 0):,.0f}")

            logger.info(f"Using strategy slippage for {token_address}: {strategy_slippage:.4f} ({current_slippage_bps} bps)")




            # Prepare metadata for the execution queue
            execution_metadata = {
                'source': signal.get('source', 'unknown'),
                'signal_source': signal.get('signal_source', signal.get('source', 'unknown')),
                'channel_id': signal.get('source_channel', 'unknown'),
                'channel_name': signal.get('channel_name', signal.get('source_channel', 'unknown')),
                'is_gmgn_channel': signal.get('is_gmgn_channel', False),
                'detection_time': signal.get('timestamp', time.time()),
                'token_symbol': analysis_result_data.get('symbol', 'Unknown'),
                'token_name': analysis_result_data.get('name', 'Unknown'),
                'initial_confidence': confidence
            }

            queued = await self.execution_queue.queue_buy(
                token_address=token_address,
                amount=buy_amount_sol,
                price=price, # Current price from analysis
                slippage_bps=current_slippage_bps,
                priority=buy_priority,
                callback=buy_execution_callback,
                event_id=event_id,
                metadata=execution_metadata
            )

            if not queued:
                logger.warning(f"Failed to queue buy order for {token_address}")
                return

            try:
                # Timeout for buy execution from config or default
                buy_timeout = self.trade_settings.get('buy_execution_timeout_seconds', 60.0)
                execution_outcome = await asyncio.wait_for(buy_order_result_future, timeout=buy_timeout)
            except asyncio.TimeoutError:
                logger.error(f"Buy execution timeout for {token_address} after {buy_timeout}s.")
                # Optionally, attempt to cancel via execution_queue if supported
                return

            # execution_outcome is expected to be a dict, e.g. {'success': True, 'tx_signature': '...', 'position_id': '...'} or {'success': False, 'error': '...'}
            # But it could also be a boolean if the trade executor returns a simple True/False
            if execution_outcome:
                # Handle the case where execution_outcome is a boolean
                if isinstance(execution_outcome, bool):
                    success = execution_outcome
                    execution_outcome = {"success": success}

                if execution_outcome.get("success"):
                    logger.info(f"Buy execution successful for {token_address}. Tx: {execution_outcome.get('tx_signature', 'N/A')}")



                    # Fetch token name/details for notification from analysis_result_data or a fresh query if needed
                    token_display_name = analysis_result_data.get('name', analysis_result_data.get('symbol', token_address))
                    if token_display_name == token_address and extracted_metrics.get('token_name'): # Fallback to signal name
                        token_display_name = extracted_metrics.get('token_name')


                    # Send Buy Notification
                    sol_price_usd = await self.get_sol_price() if hasattr(self, 'get_sol_price') else 0
                    value_usd_str = f" (~${buy_amount_sol * sol_price_usd:,.2f})" if sol_price_usd > 0 else ""

                    mode_str = "REAL"  # Real trading only
                    # Get current balance and stats - use real wallet balance
                    current_balance = await self.get_wallet_balance()
                    total_trades = self.state.total_trades
                    win_rate = self.get_win_rate()
                    total_profit = self.state.total_profit

                    # CRITICAL FIX: Get additional token metrics with CORRECT field mapping
                    # TokenAnalyzer uses 'marketCap' not 'market_cap', 'volume24h' not 'volume_24h'
                    market_cap = analysis_result_data.get('marketCap', analysis_result_data.get('market_cap', 0))
                    volume_24h = analysis_result_data.get('volume24h', analysis_result_data.get('volume_24h', 0))
                    volume_5m = analysis_result_data.get('volume_5m', 0)  # This field name is correct

                    # CRITICAL FIX: Extract liquidity data - this was missing and causing $0 liquidity!
                    liquidity = analysis_result_data.get('liquidity_usd', analysis_result_data.get('liquidity', 0))

                    # CRITICAL FIX: Use the new fixed fields from token_analyzer.py
                    # Get volatility directly from the formatted field (e.g., "+275.0%")
                    volatility = analysis_result_data.get('volatility', '0.0%')

                    # Get token age directly from the formatted field (e.g., "2m" or "16691h 13m")
                    token_age = analysis_result_data.get('token_age', 'Unknown')

                    # CRITICAL FIX: Use the new fixed field from token_analyzer.py
                    # Get transaction count directly from the fixed field
                    tx_count = analysis_result_data.get('tx_count_1h', 0)

                    # Debug logging for notification data
                    logger.info(f"NOTIFICATION DATA DEBUG for {token_address}:")
                    logger.info(f"  Market Cap: {market_cap}")
                    logger.info(f"  Liquidity: {liquidity}")  # CRITICAL FIX: Add liquidity to debug logging
                    logger.info(f"  Volume 24h: {volume_24h}")
                    logger.info(f"  Volume 5m: {volume_5m}")
                    logger.info(f"  Volatility: {volatility}")
                    logger.info(f"  Token Age: {token_age}")
                    logger.info(f"  TX Count: {tx_count}")
                    logger.info(f"  Available fields in analysis_result_data: {list(analysis_result_data.keys())}")

                    # CRITICAL FIX: Ensure numeric values for formatting, handle formatted strings
                    try:
                        market_cap = float(market_cap) if market_cap else 0

                        # CRITICAL FIX: Handle liquidity which might be a formatted string like "$45,000"
                        if isinstance(liquidity, str):
                            liquidity = float(liquidity.replace('$', '').replace(',', ''))
                        else:
                            liquidity = float(liquidity) if liquidity else 0

                        # Handle volume_24h which might be a formatted string like "$42,153"
                        if isinstance(volume_24h, str):
                            volume_24h = float(volume_24h.replace('$', '').replace(',', ''))
                        else:
                            volume_24h = float(volume_24h) if volume_24h else 0

                        # Handle volume_5m which might be a formatted string like "$3,512"
                        if isinstance(volume_5m, str):
                            volume_5m = float(volume_5m.replace('$', '').replace(',', ''))
                        else:
                            volume_5m = float(volume_5m) if volume_5m else 0

                        # Handle volatility which might be a formatted string like "+27.5%"
                        if isinstance(volatility, str):
                            volatility = float(volatility.replace('%', '').replace('+', ''))
                        else:
                            volatility = float(volatility) if volatility else 0

                        tx_count = int(tx_count) if tx_count else 0
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error converting buy notification metrics to numeric for {token_address}: {e}")
                        market_cap = liquidity = volume_24h = volume_5m = volatility = tx_count = 0

                    # Format the message using the exact format from test_telegram_notifications_format.py
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # Determine the header based on run mode
                    if mode_str.upper() == "DISABLED_SIMULATION":
                        header = "SIM BUY EXECUTED"
                    else:
                        header = "BUY EXECUTED"

                    # Get the source from execution_metadata (which contains the original signal information)
                    source_name = execution_metadata.get('source', execution_metadata.get('signal_source', 'unknown'))

                    # Calculate adaptive TP/SL for this buy notification
                    current_strategy_name = self.current_strategy if hasattr(self, 'current_strategy') and self.current_strategy else "BALANCED"
                    strategies = self.config.get_section('trading_settings').get('strategies', {})
                    strategy_params = strategies.get(current_strategy_name.upper(), {})

                    if not strategy_params:
                        strategy_params = self.tp_sl_settings

                    # Get adaptive TP/SL values for notification
                    # Extract market data from execution metadata
                    confidence = execution_metadata.get('confidence', 0.5)
                    liquidity_usd = execution_metadata.get('liquidity_usd', 0)
                    volume_5m_usd = execution_metadata.get('volume_5m', 0)

                    fixed_tp, fixed_sl = self.get_adaptive_tp_sl(
                        strategy_params,
                        confidence=confidence,
                        liquidity_usd=liquidity_usd,
                        volume_5m_usd=volume_5m_usd,
                        token_address=token_address
                    )

                    # Format exactly matching test_telegram_notifications_format.py
                    buy_notification_msg = (
                        f"🟢 [{header}] — {timestamp}\n"
                        f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                        f"📍 Signal Source: {source_name}\n"
                        f"📦 Token: {token_address}\n"
                        f"📡 Confidence Score: {confidence:.2f} (⚙️ Tech: {tech_score:.2f}, Final: {confidence:.2f})\n\n"
                        f"📈 Token Metrics:\n"
                        f" • Market Cap: ${market_cap:,.0f}\n"
                        f" • Liquidity: ${liquidity:,.0f}\n"
                        f" • 5m Volume: ${volume_5m:,.0f}\n"
                        f" • 24h Volume: ${volume_24h:,.0f}\n"
                        f" • Volatility (24h): {volatility:.1f}%\n"
                        f" • Age: {token_age}\n"
                        f" • Tx Count (1h): {tx_count:,}\n\n"
                        f"💰 Trade Executed:\n"
                        f" • Mode: {mode_str}\n"
                        f" • Amount: {buy_amount_sol} SOL{value_usd_str}\n"
                        f" • Entry Price: ${price:.8f}\n\n"
                        f"🎯 Strategy: {self.current_strategy} (TP +{fixed_tp:.1f}% / SL {fixed_sl:.1f}%)\n\n"
                        f"💼 Wallet Update:\n"
                        f" • Post-Trade Balance: {current_balance:.4f} SOL\n"
                        f" • Total Trades: {total_trades}\n"
                        f" • Winrate: {win_rate:.1f}%\n"
                        f" • PnL: {total_profit:.4f} SOL\n\n"
                        f"🔗 View Token:\n"
                        f" • [DexScreener](https://dexscreener.com/solana/{token_address})\n"
                        f" • [Solscan](https://solscan.io/token/{token_address})"
                    )

                    # Add transaction signature link if available
                    tx_signature = execution_outcome.get('tx_signature', execution_outcome.get('data', {}).get('transaction_signature', ''))
                    if tx_signature:
                        buy_notification_msg += f"\n • [Transaction](https://solscan.io/tx/{tx_signature})"
                    # Check if we should skip notifications (from position monitoring)
                    skip_notifications = analysis_result_data.get('skip_notifications', False)

                    if skip_notifications:
                        logger.info(f"Skipping buy notification for {token_address} as requested by skip_notifications flag")
                    else:
                        try:
                            # Print to console for visibility
                            print(f"\nBUY NOTIFICATION BEING SENT FROM BOT CONTROLLER\n")

                            # Send notification with high priority
                            logger.info(f"Sending buy notification for {token_address} with high priority")
                            await self.signal_handler.send_info_message(buy_notification_msg)

                            # Send balance update
                            await self.send_balance_update(reason=f"After Buy ({mode_str}) - {token_address[:8]}")

                            # Log success
                            logger.info(f"Successfully sent buy notification for {token_address}")
                        except Exception as e_notify_buy:
                            logger.error(f"Error sending buy notification or balance update: {e_notify_buy}")
                            # Try again with a different approach
                            try:
                                logger.info(f"Retrying buy notification with direct send for {token_address}")
                                await self.signal_handler._send_message_direct(f"buy_retry_{int(time.time())}", buy_notification_msg)
                            except Exception as e_retry:
                                logger.error(f"Retry also failed for buy notification: {e_retry}")

            else:
                error_msg = execution_outcome.get('error', 'Unknown error during buy execution.') if execution_outcome else 'Buy execution failed or no result.'
                logger.error(f"Buy execution failed for {token_address}: {error_msg}")
                # Send failed buy notification
                failed_buy_notification = (
                    f"❌ <b>BUY FAILED ({self.run_mode})</b>\n\n"
                    f"Token: <code>{token_address}</code>\n"
                    f"Amount: {buy_amount_sol:.4f} SOL\n"
                    f"Reason: {error_msg}"
                )
                # Check if we should skip notifications (from position monitoring)
                skip_notifications = analysis_result_data.get('skip_notifications', False)

                if skip_notifications:
                    logger.info(f"Skipping failed buy notification for {token_address} as requested by skip_notifications flag")
                else:
                    try:
                        await self.signal_handler.send_info_message(failed_buy_notification)
                    except Exception as e_notify_fail:
                        logger.error(f"Error sending failed buy notification: {e_notify_fail}")

            # SURGICAL OPTIMIZATION: Cache analysis result for deduplication using local function
            if not analysis_result_data or not analysis_result_data.get("exists", False):
                # Token was rejected - cache the rejection
                reason = analysis_result_data.get('reason', 'unknown') if analysis_result_data else 'analysis_failed'
                if 'liquidity' in reason.lower():
                    cache_token_analysis(token_address, "rejected", "low_liquidity")
                else:
                    cache_token_analysis(token_address, "rejected", "other")
            else:
                # Token was analyzed successfully
                cache_token_analysis(token_address, "analyzed", "success")

            # CRITICAL FIX: Clean up signal tracking after callback completes
            async with self.signal_lock:
                self.processing_signals.discard(token_address)
            logger.debug(f"Cleaned up signal tracking for {token_address} after callback completion")

        # End of analysis_callback

        # Determine if the token is related to an active position (e.g., for updates)
        # For a new signal, this is typically False unless it's a re-analysis trigger.
        is_active_position = self.state.has_position(token_address) # Check if we already hold this token

        # Extract any preliminary metrics from the signal itself if available
        # This could be used by TokenAnalyzer or for logging if API fails
        extracted_metrics = signal.get('metrics', {}) # Example: if signal pre-parses some data
        if not extracted_metrics and signal.get('text'): # Fallback: try to parse from raw text if available
            # (Add simple regex parsing here if signals sometimes contain raw text with numbers)
            pass


        # Priority calculation for analysis
        priority = 5  # Default analysis priority
        # Adjust priority based on source, confidence in signal, etc.
        signal_confidence_metric = signal.get('confidence', 0.0) # Confidence from the signal provider itself
        source_channel = signal.get('source_channel', '')

        if isinstance(source_channel, str):
            if 'gmgn' in source_channel.lower(): # Example: GMGN source
                priority = 1
            elif 'alpha' in source_channel.lower() or 'early' in source_channel.lower():
                priority = 2

        if signal_confidence_metric > 0.8: # High confidence from signal source
            priority = max(1, priority -1)

        if is_active_position: # Re-analysis of active position gets high priority
            priority = 0


        logger.info(f"Submitting {token_address} for analysis. Priority: {priority}, Active Position: {is_active_position}")
        # Prepare metadata with signal source information for position tracking
        signal_metadata = {
            'signal_source': signal.get('source', 'unknown'),
            'signal_data': signal, # The whole signal for context
            'is_active_position': is_active_position,
            'extracted_metrics': extracted_metrics, # Pass along any pre-parsed metrics
            'is_gmgn_channel': signal.get('is_gmgn_channel', False),  # Include GMGN channel flag
            'source': signal.get('source', 'unknown'),
            'channel_id': signal.get('source_channel', 'unknown'),
            'channel_name': signal.get('channel_name', signal.get('source_channel', 'unknown')),
            'detection_time': time.time()  # Record when we first detected this signal
        }

        # BULLETPROOF: Check permanent skip list - import from main.py
        try:
            from main import should_analyze_token_permanent
            should_analyze, reason = should_analyze_token_permanent(token_address)
        except ImportError as e:
            logger.error(f"Failed to import should_analyze_token_permanent from main.py: {e}")
            # Fallback: Allow analysis if import fails (safer than blocking)
            should_analyze, reason = True, "Import failed - allowing analysis"
        except Exception as e:
            logger.error(f"Error checking permanent skip list for {token_address}: {e}")
            # Fallback: Allow analysis if check fails
            should_analyze, reason = True, "Skip check failed - allowing analysis"

        if not should_analyze:
            logger.info(f"🚫 PERMANENT SKIP: {token_address}: {reason}")
            # Clean up processing state
            async with self.signal_lock:
                self.processing_signals.discard(token_address)
            return

        # BULLETPROOF: Submit NEW SIGNAL to analysis worker pool
        submitted = await self.analysis_worker_pool.submit_analysis(
            token_address=token_address,
            metadata=signal_metadata,
            priority=priority,
            callback=analysis_callback, # The callback defined above
            # Pass is_active_position also as a top-level arg if TokenAnalyzer uses it directly
            # is_active_position=is_active_position
        )

        if not submitted:
            logger.warning(f"ANALYSIS PROCESS: Failed to submit {token_address} to analysis worker pool. Pool might be full or stopped.")
            # Mark as permanently skipped on submission failure
            try:
                from main import mark_token_permanent_decision
                mark_token_permanent_decision(token_address, "skipped", "Analysis worker pool submission failed")
            except ImportError as e:
                logger.error(f"Failed to import mark_token_permanent_decision from main.py: {e}")
            except Exception as e:
                logger.error(f"Error marking token as permanently skipped: {e}")
            # CRITICAL FIX: Only clean up if submission failed
            async with self.signal_lock:
                self.processing_signals.discard(token_address)
            logger.debug(f"ANALYSIS PROCESS: Removed {token_address} from processing signals set due to submission failure")
        else:
            logger.debug(f"Successfully submitted {token_address} for analysis.")
            # CRITICAL FIX: Do NOT clean up signal tracking here - let the callback handle it
            # The analysis_callback will handle cleanup after processing is complete
            logger.debug(f"Keeping {token_address} in processing signals set until callback completes")

        # PERMANENT FIX: Clean up old entries from last_processed_tokens (keep only last 100)
        # This cleanup is safe to do regardless of submission status
        async with self.signal_lock:
            if len(self.last_processed_tokens) > 100:
                # Remove oldest entries
                sorted_tokens = sorted(self.last_processed_tokens.items(), key=lambda x: x[1])
                for old_token, _ in sorted_tokens[:-50]:  # Keep only 50 most recent
                    del self.last_processed_tokens[old_token]

        # Periodic memory cleanup
        await self._periodic_memory_cleanup()

    async def _periodic_memory_cleanup(self):
        """Perform periodic memory cleanup to prevent memory leaks"""
        current_time = time.time()

        # Only run cleanup every hour
        if current_time - self._last_memory_cleanup < self._memory_cleanup_interval:
            return

        logger.info("🧹 Performing periodic memory cleanup...")

        try:
            # Clean up rug protection notifications (more aggressive cleanup)
            if hasattr(self, 'notified_rug_protection_tokens'):
                if len(self.notified_rug_protection_tokens) > 25:
                    # Keep only recent notifications (reduced from 25 to 15)
                    self.notified_rug_protection_tokens = set(list(self.notified_rug_protection_tokens)[-15:])
                    logger.info("Cleaned up rug protection notifications")

            # Clean up old buy/sell event IDs (more aggressive cleanup)
            if hasattr(self, 'notified_buy_event_ids'):
                if len(self.notified_buy_event_ids) > 50:
                    self.notified_buy_event_ids = set(list(self.notified_buy_event_ids)[-25:])
                    logger.info("Cleaned up buy event IDs")

            if hasattr(self, 'notified_sell_event_ids'):
                if len(self.notified_sell_event_ids) > 50:
                    self.notified_sell_event_ids = set(list(self.notified_sell_event_ids)[-25:])
                    logger.info("Cleaned up sell event IDs")

            # Clean up token analyzer caches
            if hasattr(self, 'token_analyzer') and self.token_analyzer:
                try:
                    await self.token_analyzer.cleanup_old_cache_entries()
                    logger.info("Cleaned up token analyzer caches")
                except Exception as e:
                    logger.warning(f"Error cleaning token analyzer cache: {e}")

            # Update cleanup timestamp
            self._last_memory_cleanup = current_time
            logger.info("✅ Memory cleanup completed")

        except Exception as e:
            logger.error(f"Error during memory cleanup: {e}")

    # Ensure _get_price_history, _get_volume_history, _calculate_portfolio_risk_factor, get_sol_price are implemented
    # and TokenAnalyzer provides 'volume_5m' and other expected fields.

    async def _get_price_history(self, token_address: str) -> List[float]:
        # Placeholder: Implement actual logic to fetch price history, e.g., from DexScreener or other API
        logger.debug(f"Fetching price history for {token_address}")
        # Example: return last 100 prices if available from a cache or quick API call
        # This should be properly implemented to feed the TechnicalAnalyzer
        try:
            # Attempt to get historical data from TokenAnalyzer cache or a quick query
            # This is a simplified example. A real implementation would hit an API.
            cached_data = self.token_analyzer.get_cached_token_data(token_address)

            # Handle different response formats from DexScreener API
            if cached_data:
                # Case 1: Data is in the expected format with 'raw_data' and 'dexscreener'
                if 'raw_data' in cached_data and 'dexscreener' in cached_data['raw_data']:
                    dex_data = cached_data['raw_data']['dexscreener']

                    # Handle case where dex_data is a list (direct array of pairs)
                    if isinstance(dex_data, list):
                        pairs = dex_data
                        logger.debug(f"DexScreener data for {token_address} is a direct list of pairs")
                    # Handle case where dex_data is a dict with 'pairs' property
                    elif isinstance(dex_data, dict) and 'pairs' in dex_data:
                        pairs = dex_data.get('pairs', [])
                        logger.debug(f"DexScreener data for {token_address} is a dict with 'pairs' property")
                    else:
                        pairs = []
                        logger.warning(f"Unexpected DexScreener data format for {token_address}")

                    if pairs and isinstance(pairs, list) and len(pairs) > 0:
                        # Assuming the first pair is the most relevant one
                        current_price = cached_data.get('price', 0.0)
                        if current_price > 0:
                            # Simulate a list of 20 prices around the current price for TA demo
                            return [current_price * random.uniform(0.98, 1.02) for _ in range(20)]

                # Case 2: Data might be a direct DexScreener response
                elif isinstance(cached_data, dict) and 'pairs' in cached_data:
                    pairs = cached_data.get('pairs', [])
                    if pairs and isinstance(pairs, list) and len(pairs) > 0:
                        try:
                            current_price = float(pairs[0].get('priceUsd', 0.0))
                            if current_price > 0:
                                # Simulate a list of 20 prices around the current price for TA demo
                                return [current_price * random.uniform(0.98, 1.02) for _ in range(20)]
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error parsing price from DexScreener data: {e}")

                # Case 3: Data might be a direct list of pairs
                elif isinstance(cached_data, list) and len(cached_data) > 0:
                    try:
                        current_price = float(cached_data[0].get('priceUsd', 0.0))
                        if current_price > 0:
                            # Simulate a list of 20 prices around the current price for TA demo
                            return [current_price * random.uniform(0.98, 1.02) for _ in range(20)]
                    except (ValueError, TypeError, AttributeError) as e:
                        logger.warning(f"Error parsing price from DexScreener list data: {e}")

            logger.warning(f"No suitable price history found for {token_address} in cache for TA.")
            return []
        except Exception as e:
            logger.error(f"Error fetching price history for {token_address}: {e}")
            return []

    async def _get_volume_history(self, token_address: str) -> List[float]:
        # Placeholder: Implement actual logic to fetch volume history
        logger.debug(f"Fetching volume history for {token_address}")
        # Similar to price history, this needs a proper implementation.
        # Dexscreener API for historical candles would include volume.
        # For now, simulate some volume data.
        try:
            cached_data = self.token_analyzer.get_cached_token_data(token_address)

            # Handle different response formats from DexScreener API
            if cached_data:
                # Case 1: Data is in the expected format with 'raw_data' and 'dexscreener'
                if 'raw_data' in cached_data and 'dexscreener' in cached_data['raw_data']:
                    dex_data = cached_data['raw_data']['dexscreener']

                    # Handle case where dex_data is a list (direct array of pairs)
                    if isinstance(dex_data, list):
                        pairs = dex_data
                        logger.debug(f"DexScreener data for {token_address} is a direct list of pairs")
                    # Handle case where dex_data is a dict with 'pairs' property
                    elif isinstance(dex_data, dict) and 'pairs' in dex_data:
                        pairs = dex_data.get('pairs', [])
                        logger.debug(f"DexScreener data for {token_address} is a dict with 'pairs' property")
                    else:
                        pairs = []
                        logger.warning(f"Unexpected DexScreener data format for {token_address}")

                    if pairs and isinstance(pairs, list) and len(pairs) > 0:
                        try:
                            current_volume_5m = pairs[0].get('volume', {}).get('m5', 0.0)
                            if current_volume_5m > 0:
                                # Simulate a list of 20 volume figures around the current 5m volume
                                return [current_volume_5m * random.uniform(0.8, 1.2) for _ in range(20)]
                        except (AttributeError, TypeError) as e:
                            logger.warning(f"Error extracting volume from pairs: {e}")

                # Case 2: Data might be a direct DexScreener response
                elif isinstance(cached_data, dict) and 'pairs' in cached_data:
                    pairs = cached_data.get('pairs', [])
                    if pairs and isinstance(pairs, list) and len(pairs) > 0:
                        try:
                            volume_data = pairs[0].get('volume', {})
                            # Try to get 5m volume, fall back to 1h volume divided by 12 if not available
                            if isinstance(volume_data, dict):
                                current_volume_5m = volume_data.get('m5', volume_data.get('h1', 0.0) / 12)
                            else:
                                current_volume_5m = 0.0

                            if current_volume_5m > 0:
                                # Simulate a list of 20 volume figures around the current 5m volume
                                return [current_volume_5m * random.uniform(0.8, 1.2) for _ in range(20)]
                        except (ValueError, TypeError, AttributeError) as e:
                            logger.warning(f"Error parsing volume from DexScreener data: {e}")

                # Case 3: Data might be a direct list of pairs
                elif isinstance(cached_data, list) and len(cached_data) > 0:
                    try:
                        volume_data = cached_data[0].get('volume', {})
                        # Try to get 5m volume, fall back to 1h volume divided by 12 if not available
                        if isinstance(volume_data, dict):
                            current_volume_5m = volume_data.get('m5', volume_data.get('h1', 0.0) / 12)
                        else:
                            current_volume_5m = 0.0

                        if current_volume_5m > 0:
                            # Simulate a list of 20 volume figures around the current 5m volume
                            return [current_volume_5m * random.uniform(0.8, 1.2) for _ in range(20)]
                    except (ValueError, TypeError, AttributeError) as e:
                        logger.warning(f"Error parsing volume from DexScreener list data: {e}")

            logger.warning(f"No suitable volume history found for {token_address} in cache for TA.")
            return []
        except Exception as e:
            logger.error(f"Error fetching volume history for {token_address}: {e}")
            return []

    def _calculate_portfolio_risk_factor(self) -> float:
        # Placeholder for portfolio risk calculation
        # This factor can adjust trade size based on overall portfolio exposure,
        # number of open trades, recent performance, etc.
        # Example: if many trades are open, or recent PnL is negative, reduce risk factor.

        # For now, a simple implementation:
        open_positions_count = len(self.state.get_open_positions())
        max_trades = self.max_concurrent_trades

        if open_positions_count >= max_trades:
            return 0.5 # Reduce significantly if at max trades (though handle_new_signal checks this already)
        elif open_positions_count >= max_trades * 0.75:
            return 0.75 # Scale down if many trades are open

        return 1.0 # Default: no adjustment or full allowed risk


    async def get_sol_price(self) -> float:
        # Attempt to get SOL price from TokenAnalyzer or a dedicated price feed
        try:
            sol_data = await self.token_analyzer.get_sol_price_data() # Assuming TokenAnalyzer has this method
            if sol_data and sol_data.get('price_usd'):
                return float(sol_data['price_usd'])
            logger.warning("Could not fetch SOL price from TokenAnalyzer.")
        except Exception as e:
            logger.error(f"Error fetching SOL price: {e}")

        # Fallback or alternative method if TokenAnalyzer doesn't provide it
        # Could use a simple cache or another API endpoint just for SOL
        # For now, returning a default or potentially stale value from config if primary fails
        return self.config.get('market_data', 'sol_usd_price_fallback', default=150.0) # Example fallback

    async def _calculate_position_risk_level(self, token_address: str = None, entry_price: float = 0,
                                      current_price: float = 0, highest_price: float = 0,
                                      pnl_percent: float = 0) -> str:
        """
        Calculate risk level for a position to determine monitoring frequency.

        If called without parameters, returns a default risk level.

        Returns:
            str: Risk level - "CRITICAL", "HIGH", "MEDIUM", or "LOW"
        """
        # If no token_address provided, return default risk level
        if not token_address:
            return "MEDIUM"  # Default risk level when no specific position is provided

        # Get position data for additional context
        position = self.state.get_position(token_address)
        if not position:
            return "LOW"  # Default if position not found

        # Get price history for volatility calculation - properly await the coroutine
        try:
            price_history = await self._get_price_history(token_address)

            # Calculate volatility (if we have enough history)
            volatility = self._calculate_volatility(price_history) if len(price_history) >= 5 else 0.05
        except Exception as e:
            logger.warning(f"Error getting price history for risk calculation: {e}")
            # Default volatility if we can't get price history
            volatility = 0.05

        # Calculate time-based factors
        current_time = time.time()
        position_age_minutes = (current_time - position.get('open_time', current_time)) / 60

        # Calculate drawdown from highest point
        drawdown = (current_price - highest_price) / highest_price * 100 if highest_price > 0 else 0

        # Determine risk level based on multiple factors

        # CONFIGURABLE: Risk detection using thresholds from finalconfig.json
        # Get risk thresholds from configuration
        thresholds = getattr(self, 'risk_thresholds', {})

        # CRITICAL risk factors (immediate attention needed)
        if any([
            pnl_percent <= thresholds.get('critical_pnl_percent', -12.0),
            pnl_percent >= thresholds.get('critical_profit_percent', 40.0),
            volatility > thresholds.get('critical_volatility', 0.08),
            drawdown < thresholds.get('critical_drawdown_percent', -8.0),
            position_age_minutes < thresholds.get('critical_age_minutes', 1.5)
        ]):
            return "CRITICAL"

        # HIGH risk factors
        elif any([
            pnl_percent <= thresholds.get('high_pnl_loss_percent', -8.0) or pnl_percent >= thresholds.get('high_pnl_profit_percent', 25.0),
            volatility > thresholds.get('high_volatility', 0.04),
            drawdown < thresholds.get('high_drawdown_percent', -4.0),
            position_age_minutes < thresholds.get('high_age_minutes', 3.0),
            highest_price > entry_price * thresholds.get('high_profit_drop_threshold', 1.15) and current_price < highest_price * thresholds.get('high_profit_drop_ratio', 0.92)
        ]):
            return "HIGH"

        # MEDIUM risk factors
        elif any([
            pnl_percent <= thresholds.get('medium_pnl_loss_percent', -4.0) or pnl_percent >= thresholds.get('medium_pnl_profit_percent', 12.0),
            volatility > thresholds.get('medium_volatility', 0.015),
            position_age_minutes < thresholds.get('medium_age_minutes', 10.0),
            highest_price > entry_price * thresholds.get('medium_profit_drop_threshold', 1.08) and current_price < highest_price * thresholds.get('medium_profit_drop_ratio', 0.96)
        ]):
            return "MEDIUM"

        # Otherwise LOW risk
        else:
            return "LOW"

    def _calculate_volatility(self, price_history: List[float]) -> float:
        """Calculate price volatility from history."""
        if len(price_history) < 2:
            return 0.0

        # Calculate percentage changes
        changes = [abs((price_history[i] - price_history[i-1]) / price_history[i-1])
                  for i in range(1, len(price_history))]

        # Return average of absolute percentage changes
        return sum(changes) / len(changes)

    async def _handle_new_token_event(self, token_data: Dict[str, Any]):
        """
        Handle new token creation events from PumpPortal

        Args:
            token_data: Token data from PumpPortal
        """
        try:
            token_address = token_data.get('address')
            token_name = token_data.get('name', 'Unknown')
            token_symbol = token_data.get('symbol', 'Unknown')

            if not token_address:
                logger.warning("Received new token event without token address")
                return

            logger.info(f"New Token Detected: {token_name} ({token_symbol}) - {token_address}")
            print(f"New Token Detected: {token_name} ({token_symbol}) - {token_address}")

            # Check if we're in trading mode
            if not self.is_trading:
                logger.info("Bot is not in trading mode, skipping token analysis")
                return

            # Check if we've already traded this token
            if not self.state.can_trade(token_address, self.cooldown_seconds):
                logger.info(f"Token {token_address} is on cooldown. Skipping.")
                return

            # Check concurrent trade limit
            open_positions = self.state.get_open_positions()
            if len(open_positions) >= self.max_concurrent_trades:
                logger.info(f"Max concurrent trades ({self.max_concurrent_trades}) reached. Skipping {token_address}.")
                return

            # Create a signal for this token
            signal = {
                "token_address": token_address,
                "source": "pump_portal",
                "timestamp": token_data.get('timestamp', 0),
                "metadata": {
                    "name": token_name,
                    "symbol": token_symbol,
                    "creator": token_data.get('creator')
                }
            }

            # Process the signal
            logger.info(f"Processing new token signal for {token_address}")
            await self.handle_new_signal(signal)

        except Exception as e:
            logger.error(f"Error handling new token event: {e}", exc_info=True)
    # --- End Added Signal Processing Logic ---

    async def _check_telegram_health(self) -> bool:
        """
        Check Telegram health and handle sync issues.
        Returns True if healthy, False if issues detected.
        """
        try:
            current_time = time.time()

            # Check for Telegram sync errors every 60 seconds
            if current_time % 60 < 1 and self.signal_handler and self.signal_handler.client:
                # Check if there are any PersistentTimestampOutdatedError in logs
                has_error_in_logs = False

                # Check if client session has errors
                has_error_in_session = False
                try:
                    if hasattr(self.signal_handler.client, '_sender') and hasattr(self.signal_handler.client._sender, 'connection'):
                        if not self.signal_handler.client._sender.connection.connected.is_set():
                            has_error_in_session = True
                except Exception as e:
                    logger.warning(f"Error checking Telegram session status: {e}")

                if has_error_in_session or has_error_in_logs:
                    # Telegram sync error detected
                    if not hasattr(self, '_last_sync_error_time'):
                        self._last_sync_error_time = 0
                        self._sync_error_count = 0
                        self._max_sync_errors = 5
                        self._sync_error_window = 3600  # 1 hour

                    if current_time - self._last_sync_error_time > self._sync_error_window:
                        # Reset counter if it's been a while since the last error
                        self._sync_error_count = 1
                    else:
                        self._sync_error_count += 1

                    self._last_sync_error_time = current_time

                    logger.warning(f"Detected Telegram sync error (count: {self._sync_error_count}/{self._max_sync_errors})")

                    if self._sync_error_count >= self._max_sync_errors:
                        logger.error(f"Too many Telegram sync errors ({self._sync_error_count}), attempting to fix...")
                        await self.signal_handler.handle_telegram_sync_error()
                        self._sync_error_count = 0
                        return False

                    return False

            return True

        except Exception as e:
            logger.warning(f"Error checking for Telegram sync issues: {e}")
            return True  # Don't block monitoring on health check errors

    async def _validate_position_data(self, position: dict) -> bool:
        """
        Validate position data integrity.
        Returns True if valid, False if invalid.
        """
        try:
            # Check required fields
            required_fields = ['token_address', 'entry_price', 'token_amount', 'status']
            for field in required_fields:
                if field not in position:
                    logger.warning(f"Position missing required field: {field}")
                    return False

            # Check field values
            if not position.get('token_address'):
                logger.warning("Position has empty token_address")
                return False

            if position.get('entry_price', 0) <= 0:
                logger.warning(f"Position has invalid entry_price: {position.get('entry_price')}")
                return False

            if position.get('token_amount', 0) <= 0:
                logger.warning(f"Position has invalid token_amount: {position.get('token_amount')}")
                return False

            if position.get('status') != 'OPEN':
                logger.debug(f"Position status is not OPEN: {position.get('status')}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating position data: {e}")
            return False

    async def _evaluate_sell_conditions(self, position: dict, current_data: dict, pnl_percent: float, peak_pnl_percent: float, position_age_minutes: float) -> dict:
        """
        Evaluate all sell conditions for a position.
        Returns dict with 'should_sell', 'reason', 'sell_fraction', 'stop_loss_triggered'
        """
        try:
            token_address = position.get('token_address')
            entry_price = position.get('entry_price', 0)
            token_amount = position.get('token_amount', 0)
            partial_taken = position.get('partial_taken', False)
            current_price = current_data.get('price', 0)

            # Initialize sell decision
            sell_decision = {
                'should_sell': False,
                'reason': None,
                'sell_fraction': 1.0,
                'stop_loss_triggered': False
            }

            # --- Real-time Rug Protection Monitoring ---
            rug_alert = await self._check_realtime_rug_signals(token_address, position, current_data)
            if rug_alert:
                sell_decision.update({
                    'should_sell': True,
                    'reason': rug_alert,
                    'sell_fraction': 1.0,
                    'stop_loss_triggered': True
                })
                logger.warning(f"🚨 REAL-TIME RUG ALERT: {rug_alert} for {token_address}")
                return sell_decision

            # --- Apply Enhanced TP/SL Logic with Dynamic Adjustments ---
            # Check if the position is still in cooldown period (prevent immediate selling)
            purchase_time = position.get('purchase_time', 0)
            time_since_purchase = time.time() - purchase_time
            min_hold_time_seconds = self.trade_settings.get('trade_cooldown_seconds', 15)

            # Enforce minimum hold time (except for emergency stop losses)
            if time_since_purchase < min_hold_time_seconds:
                logger.debug(f"Position {token_address} still in cooldown period ({time_since_purchase:.1f}s < {min_hold_time_seconds}s). Skipping sell check.")
                return sell_decision

            # --- CRITICAL FIX: Enforce Maximum Hold Time ---
            enforce_max_hold = self.trade_settings.get('enforce_max_hold_time', True)
            max_hold_time_seconds = self.max_hold_time_minutes * 60

            if enforce_max_hold and time_since_purchase >= max_hold_time_seconds:
                sell_decision.update({
                    'should_sell': True,
                    'reason': f"Max hold time exceeded ({time_since_purchase/60:.1f}m >= {self.max_hold_time_minutes}m)",
                    'sell_fraction': 1.0,
                    'stop_loss_triggered': False
                })
                logger.warning(f"🕐 MAX HOLD TIME EXCEEDED: {sell_decision['reason']} for {token_address}")
                return sell_decision

            # Get position metadata for additional context
            position_metadata = position.get('metadata', {})
            original_confidence = position_metadata.get('original_confidence', 0.5)
            fresh_confidence = current_data.get('confidence', 0.0)

            # CRITICAL FIX: Use original confidence if fresh confidence is 0 or very low
            if fresh_confidence <= 0.1:
                current_confidence = original_confidence
                logger.warning(f"Using original confidence {original_confidence:.3f} for {token_address} (fresh: {fresh_confidence:.3f})")
            else:
                current_confidence = fresh_confidence
                logger.debug(f"Using fresh confidence {fresh_confidence:.3f} for {token_address}")

            # Get current strategy and calculate adaptive TP/SL
            default_strategy = self.config.get_section('trading_settings').get('default_strategy_name', 'AGGRESSIVE')
            current_strategy_name = self.current_strategy if hasattr(self, 'current_strategy') and self.current_strategy else default_strategy

            strategies = self.config.get_section('trading_settings').get('strategies', {})
            strategy_params = strategies.get(current_strategy_name.upper(), {})

            if not strategy_params:
                logger.warning(f"Strategy '{current_strategy_name}' not found or empty in config. Falling back to global tp_sl_settings.")
                strategy_params = self.tp_sl_settings

            # Use adaptive TP/SL based on current market conditions
            liquidity_usd = current_data.get('liquidity_usd', 0)
            volume_5m_usd = current_data.get('volume_5m', 0)

            fixed_tp1, fixed_stop_loss = self.get_adaptive_tp_sl(
                strategy_params,
                confidence=current_confidence,
                liquidity_usd=liquidity_usd,
                volume_5m_usd=volume_5m_usd,
                token_address=token_address
            )

            # Log TP/SL calculation details
            use_adaptive = strategy_params.get('use_adaptive_tp_sl', False)
            logger.info(f"{'ADAPTIVE' if use_adaptive else 'FIXED'} TP/SL CALCULATION for {token_address}:")
            logger.info(f"  Strategy: {current_strategy_name}")
            logger.info(f"  Confidence: {current_confidence:.3f}")
            logger.info(f"  Liquidity: ${liquidity_usd:,.2f}")
            logger.info(f"  Volume 5m: ${volume_5m_usd:,.2f}")
            logger.info(f"  Calculated TP: {fixed_tp1:.2f}%")
            logger.info(f"  Calculated SL: {fixed_stop_loss:.2f}%")
            logger.info(f"  Current PnL: {pnl_percent:.2f}%")

            # Get configured values
            tp1 = self.tp_sl_settings.get('pnl_sell_percent_min', 15.0)
            hard_sl = self.tp_sl_settings.get('stop_loss_percent', -30.0)
            fixed_partial_frac = strategy_params.get('partial_sell_fraction', 0.8)

            logger.debug(f"SELL TRIGGER CHECK for {token_address}: PnL={pnl_percent:.2f}%, TP1={fixed_tp1:.1f}%, Fixed SL={fixed_stop_loss:.1f}%, PartialTaken={partial_taken}")

            # 1. Fixed Stop Loss (only if partial not taken yet)
            logger.info(f"STOP LOSS CHECK for {token_address}: partial_taken={partial_taken}, pnl_percent={pnl_percent:.2f}%, fixed_stop_loss={fixed_stop_loss:.2f}%")

            if not partial_taken and pnl_percent <= fixed_stop_loss:
                sell_decision.update({
                    'should_sell': True,
                    'reason': f"Stop Loss hit ({pnl_percent:.1f}% <= {fixed_stop_loss:.1f}%)",
                    'sell_fraction': 1.0,
                    'stop_loss_triggered': True
                })
                logger.info(f"SELL TRIGGER: {sell_decision['reason']} for {token_address}")
                return sell_decision

            # 2. First Take Profit (Full Sell)
            tp1_triggered = False
            if not partial_taken and not sell_decision['stop_loss_triggered']:
                logger.info(f"TP1 EVALUATION for {token_address}:")
                logger.info(f"  Current PnL: {pnl_percent:.2f}%")
                logger.info(f"  Fixed TP1: {fixed_tp1:.2f}%")
                logger.info(f"  Base TP1: {tp1:.2f}%")

                # Check fixed TP1 first
                if pnl_percent >= fixed_tp1:
                    sell_decision.update({
                        'should_sell': True,
                        'reason': f"TP1 hit ({pnl_percent:.1f}% >= {fixed_tp1:.1f}%)",
                        'sell_fraction': 1.0  # FULL SELL instead of partial
                    })
                    tp1_triggered = True
                    logger.info(f"SELL TRIGGER: {sell_decision['reason']} for {token_address}")
                    return sell_decision
                # Fallback: Check base TP1 if fixed failed or is 0
                elif fixed_tp1 <= 0 and pnl_percent >= tp1:
                    sell_decision.update({
                        'should_sell': True,
                        'reason': f"Base TP1 hit ({pnl_percent:.1f}% >= {tp1:.1f}%)",
                        'sell_fraction': 1.0
                    })
                    tp1_triggered = True
                    logger.info(f"SELL TRIGGER (FALLBACK): {sell_decision['reason']} for {token_address}")
                    return sell_decision
                # Emergency fallback: If both fixed and base TP are 0 or invalid, use 15% default
                elif fixed_tp1 <= 0 and tp1 <= 0 and pnl_percent >= 15.0:
                    sell_decision.update({
                        'should_sell': True,
                        'reason': f"Emergency TP hit ({pnl_percent:.1f}% >= 15.0%)",
                        'sell_fraction': 1.0
                    })
                    tp1_triggered = True
                    logger.warning(f"SELL TRIGGER (EMERGENCY): {sell_decision['reason']} for {token_address} - using 15% default TP")
                    return sell_decision

            return sell_decision

        except Exception as e:
            logger.error(f"Error evaluating sell conditions for {position.get('token_address', 'unknown')}: {e}")
            return {
                'should_sell': False,
                'reason': None,
                'sell_fraction': 1.0,
                'stop_loss_triggered': False
            }

    async def _execute_sell_decision(self, position: dict, sell_decision: dict, current_data: dict) -> bool:
        """
        Execute the sell decision for a position.
        Returns True if execution was successful, False otherwise.
        """
        try:
            token_address = position.get('token_address')
            entry_price = position.get('entry_price', 0)
            token_amount = position.get('token_amount', 0)
            current_price = current_data.get('price', 0)

            reason_to_sell = sell_decision['reason']
            sell_fraction = sell_decision['sell_fraction']
            stop_loss_triggered = sell_decision['stop_loss_triggered']

            # Generate sell event ID
            sell_event_id = f"sell_{token_address}_{reason_to_sell.replace(' ', '_').replace('%', 'pct')}_{int(time.time())}"

            logger.info(f"SELL Triggered for {token_address}: {reason_to_sell}, Fraction: {sell_fraction*100:.1f}% PNL: {((current_price - entry_price) / entry_price) * 100:.2f}%")

            # Comprehensive pre-execution validation
            validation_errors = []

            # Basic parameter validation
            if token_amount <= 0:
                validation_errors.append(f"Invalid token amount: {token_amount}")
            if sell_fraction <= 0 or sell_fraction > 1:
                validation_errors.append(f"Invalid sell fraction: {sell_fraction}")

            # Price validation for critical situations
            try:
                last_price_update = position.get('last_price_update', 0)
                time_since_price_update = time.time() - last_price_update

                # Determine if fresh price check is warranted
                needs_fresh_price = False
                if stop_loss_triggered and time_since_price_update > 2.0:
                    needs_fresh_price = True
                elif ((current_price - entry_price) / entry_price) * 100 <= -30.0 and time_since_price_update > 1.0:
                    needs_fresh_price = True
                elif abs(((current_price - entry_price) / entry_price) * 100) >= 40.0 and time_since_price_update > 1.5:
                    needs_fresh_price = True

                if needs_fresh_price:
                    fresh_analysis = await self._get_fresh_price_data(token_address)
                    if fresh_analysis and fresh_analysis.get("exists") and fresh_analysis.get("price", 0) > 0:
                        fresh_price = fresh_analysis["price"]
                        price_deviation = abs(fresh_price - current_price) / current_price * 100

                        if price_deviation > 10.0:
                            validation_errors.append(f"Price moved {price_deviation:.1f}% during validation")
                            current_price = fresh_price
                            logger.warning(f"PRICE VALIDATION: Updated price for {token_address}: ${fresh_price:.8f}")

                        # Update position with fresh price
                        self.state.update_position_price(token_address, fresh_price, time.time())
                    else:
                        validation_errors.append("Cannot get fresh price data for critical validation")
            except Exception as e:
                validation_errors.append(f"Price validation error: {str(e)}")

            # Liquidity validation
            try:
                current_liquidity = current_data.get("liquidity_usd", 0)
                if current_liquidity < 500:
                    validation_errors.append(f"Extremely low liquidity: ${current_liquidity:.0f}")
                    logger.warning(f"LIQUIDITY WARNING: Very low liquidity for {token_address}: ${current_liquidity:.0f}")
            except Exception as e:
                validation_errors.append(f"Liquidity validation error: {str(e)}")

            # Log validation results
            if validation_errors:
                logger.warning(f"SELL VALIDATION WARNINGS for {token_address}: {'; '.join(validation_errors)}")

            # Only block execution for critical errors
            critical_errors = [err for err in validation_errors if any(critical in err.lower() for critical in ['invalid token amount', 'invalid sell fraction'])]
            if critical_errors:
                logger.error(f"SELL VALIDATION FAILED for {token_address}: {'; '.join(critical_errors)}")
                return False

            # Execute the sell order
            # Real trading only - simulation mode removed

            # Get strategy-specific slippage
            current_market_data = {
                'liquidity_usd': position.get('liquidity_usd', 0),
                'volume_5m': position.get('volume_5m', 0),
                'volume_24h': position.get('volume_24h', 0),
                'market_cap': position.get('market_cap', 0)
            }
            strategy_slippage = self.get_strategy_slippage(token_address, current_market_data)
            sell_slippage_bps = int(strategy_slippage * 10000)

            logger.info(f"Using strategy slippage for sell {token_address}: {strategy_slippage:.4f} ({sell_slippage_bps} bps)")

            # Set execution flag
            position['execution_in_progress'] = True
            position['execution_reason'] = reason_to_sell

            # Queue the sell order
            sell_result_future = asyncio.Future()

            async def sell_callback(result):
                # CRITICAL FIX: Validate result format before setting future
                try:
                    if not isinstance(result, dict):
                        logger.error(f"CRITICAL: Sell callback received non-dict result: {type(result)} - {result}")
                        result = {'success': False, 'error': 'Invalid result type', 'tx_signature': '', 'data': {}}

                    logger.info(f"🔄 Sell callback received result for {token_address}: success={result.get('success', False)}")
                    sell_result_future.set_result(result)

                except Exception as e:
                    logger.error(f"❌ CRITICAL ERROR in sell callback for {token_address}: {e}", exc_info=True)
                    # Set a failure result to prevent hanging
                    error_result = {'success': False, 'error': str(e), 'tx_signature': '', 'data': {}}
                    sell_result_future.set_result(error_result)

            sell_priority = 0 if stop_loss_triggered else 2

            # PERMANENT FIX: Ensure execution queue is running before attempting to queue sell
            if not self.execution_queue.is_running:
                logger.warning(f"Execution queue not running, starting it now for sell {token_address}")
                try:
                    await self.execution_queue.start()
                    logger.info("✅ Execution queue started for sell operation")
                except Exception as e:
                    logger.error(f"❌ CRITICAL: Failed to start execution queue for sell: {e}")
                    position['execution_in_progress'] = False
                    position.pop('execution_reason', None)
                    return False

            queued = await self.execution_queue.queue_sell(
                token_address=token_address,
                token_amount=token_amount,
                entry_price=entry_price,
                slippage_bps=sell_slippage_bps,
                sell_fraction=sell_fraction,
                priority=sell_priority,
                callback=sell_callback,
                event_id=sell_event_id
            )

            if not queued:
                position['execution_in_progress'] = False
                position.pop('execution_reason', None)
                logger.warning(f"Failed to queue sell order for {token_address}")
                return False

            # Wait for sell execution to complete
            try:
                sell_result = await asyncio.wait_for(sell_result_future, timeout=60.0)
                position['execution_in_progress'] = False
                position.pop('execution_reason', None)

                sell_success = sell_result.get('success', False) if isinstance(sell_result, dict) else False
                sell_verified = sell_result.get('verified', False) if isinstance(sell_result, dict) else False

                if sell_success and sell_verified:
                    # Handle successful and verified sell
                    logger.info(f"✅ Sell VERIFIED successful for {token_address}")
                    await self._handle_successful_sell(position, current_price, reason_to_sell, sell_fraction, sell_event_id, sell_result)
                    return True
                elif sell_success and not sell_verified:
                    logger.error(f"❌ Sell sent but FAILED verification for {token_address} (EventID: {sell_event_id})")
                    logger.error(f"❌ Transaction may have failed: {sell_result.get('transaction_signature', 'N/A')}")
                    return False
                else:
                    logger.error(f"Sell command FAILED for {token_address} (EventID: {sell_event_id})")

                    # CRITICAL FIX: Track failed sell attempts to prevent infinite retries
                    if not hasattr(self, '_failed_sell_attempts'):
                        self._failed_sell_attempts = {}

                    self._failed_sell_attempts[token_address] = self._failed_sell_attempts.get(token_address, 0) + 1

                    # If token has failed 3+ times, mark it as unsellable
                    if self._failed_sell_attempts[token_address] >= 3:
                        logger.warning(f"🚫 BLACKLISTING TOKEN: {token_address} failed {self._failed_sell_attempts[token_address]} sell attempts")
                        logger.warning(f"🚫 Marking position as FAILED to prevent further sell attempts")

                        # Mark position as failed to stop retry attempts
                        try:
                            self.state.update_position_status(token_address, 'FAILED')
                            logger.info(f"✅ Position {token_address} marked as FAILED - no more sell attempts")
                        except Exception as e:
                            logger.error(f"❌ Failed to update position status: {e}")

                    return False

            except asyncio.TimeoutError:
                position['execution_in_progress'] = False
                position.pop('execution_reason', None)
                logger.error(f"Sell execution timeout for {token_address} (EventID: {sell_event_id})")
                return False
            except Exception as e:
                position['execution_in_progress'] = False
                position.pop('execution_reason', None)
                logger.error(f"Error waiting for sell execution for {token_address} (EventID: {sell_event_id}): {e}")
                return False

        except Exception as e:
            logger.error(f"Error executing sell decision for {position.get('token_address', 'unknown')}: {e}")
            return False

    async def _handle_successful_sell(self, position: dict, current_price: float, reason_to_sell: str, sell_fraction: float, sell_event_id: str, sell_result: dict = None):
        """Handle successful sell execution including notifications and state updates."""
        try:
            token_address = position.get('token_address')
            entry_price = position.get('entry_price', 0)

            # Calculate final PnL
            final_pnl_percent = ((current_price - entry_price) / entry_price) * 100 if entry_price > 0 else 0

            # CRITICAL FIX: Don't close position again - trade_executor already closed it
            # Just verify it was closed successfully
            remaining_positions = self.state.get_open_positions()
            if token_address in remaining_positions:
                logger.warning(f"⚠️ Position {token_address} still exists in StateManager after sell execution")
                # This is unusual but not critical - the sell was successful
            else:
                logger.info(f"✅ Verified: Position {token_address} successfully removed from StateManager")

            # Send sell notification
            await self._send_sell_notification(position, current_price, reason_to_sell, sell_fraction, sell_event_id, final_pnl_percent, sell_result)

            # Send balance update
            try:
                await self.send_balance_update(reason="After Sell")
            except Exception as balance_error:
                logger.error(f"Error sending balance update after sell: {balance_error}")

        except Exception as e:
            logger.error(f"Error handling successful sell for {position.get('token_address', 'unknown')}: {e}")

    async def _check_realtime_rug_signals(self, token_address: str, position: dict, current_data: dict) -> str:
        """
        Check for real-time rug pull signals.
        Returns rug alert message if detected, None otherwise.
        """
        try:
            current_liquidity = current_data.get("liquidity_usd", 0)
            position_metadata = position.get('metadata', {})
            initial_liquidity = position_metadata.get("liquidity", 0)

            # 1. Zero liquidity = immediate rug detection
            if current_liquidity <= 0:
                return f"EMERGENCY RUG DETECTED - ZERO LIQUIDITY (${current_liquidity:.0f})"

            # 2. Absolute liquidity threshold (any token with <$100 liquidity is rugged)
            if current_liquidity < 100:
                return f"EMERGENCY RUG DETECTED - LIQUIDITY COLLAPSED (${current_liquidity:.0f} < $100)"

            # 3. Massive liquidity drain (90%+ drop = instant rug)
            if initial_liquidity > 1000:
                liquidity_drop_percent = ((initial_liquidity - current_liquidity) / initial_liquidity) * 100
                if liquidity_drop_percent >= 90:
                    return f"MASSIVE RUG DETECTED - {liquidity_drop_percent:.1f}% LIQUIDITY DROP (${initial_liquidity:.0f} → ${current_liquidity:.0f})"
                elif liquidity_drop_percent >= 70:
                    return f"SEVERE LIQUIDITY DRAIN - {liquidity_drop_percent:.1f}% DROP (${initial_liquidity:.0f} → ${current_liquidity:.0f})"

            # 4. Volume drop protection
            current_volume = current_data.get("volume_5m", 0)
            initial_volume = position_metadata.get("volume_5m", 0)
            if initial_volume > 0 and current_volume < initial_volume * 0.3:  # 70% volume drop
                return f"Volume drop protection ({current_volume:.2f} < {initial_volume * 0.3:.2f})"

            return None

        except Exception as e:
            logger.error(f"Error checking rug signals for {token_address}: {e}")
            return None

    async def _get_fresh_price_data(self, token_address: str) -> dict:
        """Get fresh price data for critical validation."""
        try:
            if self.fast_analysis_function and callable(self.fast_analysis_function):
                return await asyncio.wait_for(
                    self.fast_analysis_function(token_address, force_fresh=True, skip_notifications=True),
                    timeout=2.0
                )
            return None
        except Exception as e:
            logger.error(f"Error getting fresh price data for {token_address}: {e}")
            return None

    async def _send_sell_notification(self, position: dict, current_price: float, reason_to_sell: str, sell_fraction: float, sell_event_id: str, final_pnl_percent: float, sell_result: dict = None):
        """Send sell notification to Telegram."""
        try:
            token_address = position.get('token_address')
            entry_price = position.get('entry_price', 0)
            metadata = position.get('metadata', {})
            source = metadata.get('source', 'unknown')

            # Check if notification already sent
            if sell_event_id in self.notified_sell_event_ids:
                logger.info(f"Sell notification for {token_address} (EventID: {sell_event_id}) already sent. Skipping duplicate.")
                return

            # Calculate holding duration
            start_time = metadata.get('timestamp', time.time() - 3600)
            holding_duration_seconds = time.time() - start_time

            if holding_duration_seconds < 60:
                holding_duration = f"{int(holding_duration_seconds)} seconds"
            elif holding_duration_seconds < 3600:
                holding_duration = f"{int(holding_duration_seconds / 60)} minutes"
            else:
                hours = int(holding_duration_seconds / 3600)
                minutes = int((holding_duration_seconds % 3600) / 60)
                holding_duration = f"{hours}h {minutes}m"

            # Get token metrics with multiple fallback attempts
            liquidity = 0
            market_cap = 0
            volume_24h = 0
            metrics_fetched = False

            # Attempt 1: Fresh analysis
            if self.fast_analysis_function and callable(self.fast_analysis_function):
                try:
                    logger.info(f"🔄 Attempting fresh analysis for sell notification: {token_address}")
                    analysis = await asyncio.wait_for(
                        self.fast_analysis_function(token_address, force_fresh=True, skip_notifications=True, bypass_rate_limit=True, bypass_permanent_check=True),
                        timeout=8.0  # CRITICAL FIX: Bypass permanent token tracking for sell notifications
                    )

                    if analysis and analysis.get('exists', False):
                        liquidity = analysis.get('liquidity_usd', 0)
                        market_cap = analysis.get('market_cap', 0)
                        volume_24h = analysis.get('volume_24h', 0)

                        logger.info(f"📊 Fresh analysis result: MC=${market_cap:,.0f}, Liq=${liquidity:,.0f}, Vol=${volume_24h:,.0f}")

                        if liquidity > 0 or market_cap > 0 or volume_24h > 0:
                            metrics_fetched = True
                            logger.info(f"✅ Fresh token metrics fetched for sell notification")
                        else:
                            logger.warning(f"⚠️ Fresh analysis returned zero values for all metrics")
                    else:
                        logger.warning(f"⚠️ Fresh analysis failed: exists={analysis.get('exists', False) if analysis else 'None'}")

                except Exception as e:
                    logger.error(f"❌ Error fetching fresh token metrics: {e}")

            # Attempt 2: Cached analysis if fresh failed
            if not metrics_fetched and self.fast_analysis_function and callable(self.fast_analysis_function):
                try:
                    logger.info(f"🔄 Attempting cached analysis for sell notification: {token_address}")
                    analysis = await asyncio.wait_for(
                        self.fast_analysis_function(token_address, force_fresh=False, skip_notifications=True, bypass_rate_limit=True, bypass_permanent_check=True),
                        timeout=6.0  # CRITICAL FIX: Bypass permanent token tracking for sell notifications
                    )

                    if analysis and analysis.get('exists', False):
                        liquidity = analysis.get('liquidity_usd', 0)
                        market_cap = analysis.get('market_cap', 0)
                        volume_24h = analysis.get('volume_24h', 0)

                        logger.info(f"📊 Cached analysis result: MC=${market_cap:,.0f}, Liq=${liquidity:,.0f}, Vol=${volume_24h:,.0f}")

                        if liquidity > 0 or market_cap > 0 or volume_24h > 0:
                            metrics_fetched = True
                            logger.info(f"✅ Cached token metrics fetched for sell notification")
                        else:
                            logger.warning(f"⚠️ Cached analysis returned zero values for all metrics")
                    else:
                        logger.warning(f"⚠️ Cached analysis failed: exists={analysis.get('exists', False) if analysis else 'None'}")

                except Exception as e:
                    logger.error(f"❌ Error fetching cached token metrics: {e}")

            # Final fallback logging
            if not metrics_fetched:
                logger.warning(f"⚠️ SELL NOTIFICATION: No metrics fetched for {token_address}, using default values (MC=$0, Liq=$0, Vol=$0)")

            # Get TP/SL settings for notification
            strategies = self.config.get_section('trading_settings').get('strategies', {})
            current_strategy_name = self.current_strategy if hasattr(self, 'current_strategy') and self.current_strategy else 'AGGRESSIVE'
            strategy_params = strategies.get(current_strategy_name.upper(), {})

            if not strategy_params:
                strategy_params = self.tp_sl_settings

            take_profit_target = strategy_params.get('pnl_sell_percent_min', 15.0)
            stop_loss_limit = abs(strategy_params.get('stop_loss_percent', -30.0))

            # Get current balance and stats - use real wallet balance
            current_balance = await self.get_wallet_balance()
            total_trades = self.state.total_trades
            win_rate = self.get_win_rate()
            total_profit = self.state.total_profit

            # Format the notification message
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            exit_price_str = f"${current_price:.8f} ({final_pnl_percent:+.2f}%)"

            header = "SELL"  # Real trading only - simulation mode removed

            notification_msg = (
                f"🟣 [{header} — {reason_to_sell.upper()}] — {timestamp}\n"
                f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                f"📍 Triggered by: {source}\n"
                f"📦 Token: {token_address}\n\n"
                f"📊 Token Metrics at Exit:\n"
                f" • Exit Price: {exit_price_str}\n"
                f" • Entry Price: ${entry_price:.8f}\n"
                f" • Market Cap: ${market_cap:,.0f}\n"
                f" • Liquidity: ${liquidity:,.0f}\n"
                f" • 24h Volume: ${volume_24h:,.0f}\n"
                f" • Holding Duration: {holding_duration}\n\n"
                f"🎯 Strategy: {current_strategy_name} (TP +{take_profit_target}% / SL {-stop_loss_limit}%)\n\n"
                f"💼 Wallet Update:\n"
                f" • Post-Sell Balance: {current_balance:.4f} SOL\n"
                f" • Total Trades: {total_trades}\n"
                f" • Winrate: {win_rate:.1f}%\n"
                f" • PnL: {total_profit:.4f} SOL\n\n"
                f"🔗 Token Links:\n"
                f" • [DexScreener](https://dexscreener.com/solana/{token_address})\n"
                f" • [Solscan](https://solscan.io/token/{token_address})\n"
                f"Position Closed.\n"
                f"Mode: {self.run_mode}\n"
                f"EventID: {sell_event_id}"
            )

            # Add transaction signature link if available
            if sell_result and isinstance(sell_result, dict):
                tx_signature = sell_result.get('tx_signature', sell_result.get('data', {}).get('transaction_signature', ''))
                if tx_signature:
                    notification_msg += f"\n • [Transaction](https://solscan.io/tx/{tx_signature})"

            # Send notification
            await self.signal_handler.send_info_message(notification_msg)
            logger.info(f"Sell notification sent for {token_address} (EventID: {sell_event_id})")
            self.notified_sell_event_ids.add(sell_event_id)

        except Exception as e:
            logger.error(f"Failed to send sell notification for {position.get('token_address', 'unknown')} (EventID: {sell_event_id}): {e}")

    async def _monitor_positions(self):
        """Background task to monitor open positions for TP/SL and check for Telegram sync issues."""
        logger.info("[ROCKET] ENHANCED POSITION MONITORING: Starting robust position monitoring with mandatory 5-minute exits...")
        logger.info(f"[CHART] Max concurrent trades: {self.max_concurrent_trades}")
        logger.info(f"[CLOCK] Max hold time: {self.max_hold_time_minutes} minutes (MANDATORY EXIT)")
        logger.info("[SHIELD] Enhanced features: TP/SL, Mandatory Timeout, Emergency Stops, Slot Management, Ultra-Fast Monitoring")
        logger.info("[TARGET] SLOT OPTIMIZATION: Positions will be force-exited after 5 minutes to free slots for fresh opportunities")

        # Variables for periodic balance updates
        last_balance_update_time = 0
        balance_update_interval = 3600  # Send balance update every hour

        while not self._stop_event.is_set():
            try:
                # SURGICAL FIX: Check Telegram health first
                if not await self._check_telegram_health():
                    logger.warning("⚠️ Telegram health check failed, skipping monitoring cycle")
                    await asyncio.sleep(5)
                    continue

                # Check if it's time for a periodic balance update
                current_time = time.time()
                if current_time - last_balance_update_time >= balance_update_interval:
                    try:
                        logger.info("Sending periodic balance update")
                        await self.send_balance_update(reason="Periodic Update")
                        last_balance_update_time = current_time
                    except Exception as balance_error:
                        logger.error(f"Error sending periodic balance update: {balance_error}")

                # BULLETPROOF: Get currently open positions from StateManager
                open_positions_dict = self.state.get_open_positions()
                open_position_keys = list(open_positions_dict.keys())

                if not open_position_keys:
                    await asyncio.sleep(10) # Wait longer if no positions
                    continue

                # SIMPLIFIED: Use StateManager as source of truth for positions
                valid_position_keys = list(open_position_keys)

                if not valid_position_keys:
                    logger.debug("POSITION MONITORING: No open positions to monitor")
                    await asyncio.sleep(10)
                    continue

                logger.debug(f"POSITION MONITORING: Monitoring {len(valid_position_keys)} open positions: {valid_position_keys}")
                # Real trading only - simulation mode removed

                for token_address in open_position_keys:
                    try:
                        # SURGICAL FIX: Validate position still exists and is OPEN
                        position_data = self.state.get_position(token_address)
                        # CRITICAL FIX: Properly await async validation
                        is_valid = await self._validate_position_data(position_data)
                        if not is_valid:
                            logger.debug(f"Position {token_address} validation failed, removing from monitoring")
                            self.monitored_positions.discard(token_address)
                            continue

                        # CRITICAL FIX: Skip positions marked as FAILED to prevent infinite sell retries
                        position_status = position_data.get('status', 'OPEN')
                        if position_status == 'FAILED':
                            logger.debug(f"Skipping FAILED position {token_address} - marked as unsellable")
                            self.monitored_positions.discard(token_address)
                            continue

                        # SURGICAL FIX: Check failed token blacklist before analysis
                        current_time = time.time()
                        if token_address in self.failed_tokens:
                            time_since_failure = current_time - self.failed_tokens[token_address]
                            if time_since_failure < self.failed_token_cooldown:
                                logger.debug(f"Token {token_address} in cooldown for {self.failed_token_cooldown - time_since_failure:.0f}s")
                                continue
                            else:
                                # Cooldown expired, remove from blacklist
                                del self.failed_tokens[token_address]
                                logger.debug(f"Token {token_address} removed from failed tokens blacklist")

                        # Add to monitored positions set
                        self.monitored_positions.add(token_address)

                        # Get fresh position data
                        try:
                            analysis = await self._get_fresh_position_data(token_address)
                            if not analysis or not analysis.get("exists") or analysis.get("price", 0) <= 0:
                                logger.warning(f"Could not get valid current price for {token_address} during monitoring.")
                                continue
                            current_price = analysis["price"]
                        except Exception as e:
                            logger.error(f"Error getting fresh position data for {token_address}: {e}")
                            continue

                        entry_price = position_data.get('entry_price', 0)
                        token_amount = position_data.get('token_amount', 0)

                        # Update highest price in StateManager
                        self.state.update_position_highest_price(token_address, current_price)

                        if token_amount <= 0:
                            logger.error(f"SELL DEBUG: Position {token_address} has zero token amount ({token_amount}) in state.")
                            logger.warning(f"Position {token_address} has zero token amount in state. Should be closed? Skipping monitor.")
                            continue

                        # Calculate PnL % (ensure entry_price is not zero)
                        if entry_price <= 0:
                            pnl_percent = 0.0
                        else:
                            pnl_percent = ((current_price - entry_price) / entry_price) * 100

                        # Track peak profit for trailing stop logic
                        peak_pnl_percent = position_data.get('peak_pnl_percent', pnl_percent)
                        if pnl_percent > peak_pnl_percent:
                            position_data['peak_pnl_percent'] = pnl_percent
                            position_data['peak_price'] = current_price
                            peak_pnl_percent = pnl_percent

                        # Calculate position age
                        position_age_minutes = (time.time() - position_data.get('entry_time', time.time())) / 60

                        logger.info(f"📈 {token_address[:8]}... | "
                                  f"PnL: {pnl_percent:+.1f}% (${(current_price - entry_price) * token_amount:+.2f}) | "
                                  f"Peak: {peak_pnl_percent:.1f}% | "
                                  f"Price: ${current_price:.8f} | "
                                  f"Age: {position_age_minutes:.1f}m")

                        # SURGICAL FIX: Extract sell condition evaluation
                        sell_decision = await self._evaluate_sell_conditions(
                            position_data, analysis, pnl_percent, peak_pnl_percent, position_age_minutes
                        )

                        if sell_decision['should_sell']:
                            # CRITICAL FIX: Comprehensive sell pipeline validation
                            logger.info(f"🎯 SELL TRIGGERED for {token_address}: {sell_decision['reason']}")

                            # Validate sell pipeline before execution
                            pipeline_valid = await self._validate_sell_pipeline(token_address)
                            if not pipeline_valid:
                                logger.error(f"❌ CRITICAL: Sell pipeline validation failed for {token_address}")
                                continue

                            # SURGICAL FIX: Extract sell execution
                            success = await self._execute_sell_decision(position_data, sell_decision, analysis)
                            if success:
                                logger.info(f"✅ Successfully executed sell for {token_address}")
                            else:
                                logger.error(f"❌ Failed to execute sell for {token_address}")
                                # Log detailed failure information
                                logger.error(f"❌ Sell failure details: position={position_data}, decision={sell_decision}")

                    except Exception as e:
                        logger.error(f"❌ Error processing position {token_address}: {e}", exc_info=True)
                        continue

                        # All sell condition logic has been moved to _evaluate_sell_conditions function
                        # All validation, execution, and notification logic has been moved to extracted functions

                    # All execution logic has been moved to _execute_sell_decision function

                    # All notification and error handling logic has been moved to extracted functions

                # Sleep between monitoring cycles
                await asyncio.sleep(self.monitoring_interval)

            except asyncio.CancelledError:
                logger.info("Position monitoring loop cancelled.")
                break
            except Exception as e:
                logger.error(f"Error in position monitoring loop: {e}", exc_info=True)

                # Send error notification to Telegram
                try:
                    error_msg = f"⚠️ <b>Position Monitoring Error</b>\n\n{str(e)}\n\nThe bot will continue monitoring after a short delay."
                    asyncio.create_task(self.signal_handler.send_info_message(error_msg))
                except Exception as notify_error:
                    logger.error(f"Failed to send error notification: {notify_error}")

                # Wait before retrying
                await asyncio.sleep(2) # Reduced from 15s to 2s for faster recovery after errors
        logger.info("Position monitoring loop stopped.")

    async def _start_position_monitoring_resilient(self):
        """Start position monitoring with resilience and retry logic."""
        logger.info("Starting resilient position monitoring...")

        self._monitor_startup_attempts = 0

        while self._monitor_startup_attempts < self._max_monitor_startup_attempts:
            try:
                self._monitor_startup_attempts += 1
                logger.info(f"Position monitoring startup attempt {self._monitor_startup_attempts}/{self._max_monitor_startup_attempts}")

                # Cancel existing monitor task if it exists
                if self._monitor_task and not self._monitor_task.done():
                    logger.info("Cancelling existing monitor task before restart")
                    self._monitor_task.cancel()
                    try:
                        await self._monitor_task
                    except asyncio.CancelledError:
                        pass

                # Create new monitor task
                self._monitor_task = asyncio.create_task(self._monitor_positions_resilient())

                # Wait a short time to ensure it starts properly
                await asyncio.sleep(0.5)

                # Check if the task is still running
                if self._monitor_task.done():
                    # Task completed immediately, likely an error
                    try:
                        await self._monitor_task  # This will raise the exception
                    except Exception as e:
                        logger.error(f"Position monitoring task failed immediately: {e}")
                        if self._monitor_startup_attempts < self._max_monitor_startup_attempts:
                            logger.info(f"Retrying position monitoring startup in {self._monitor_restart_delay} seconds...")
                            await asyncio.sleep(self._monitor_restart_delay)
                            continue
                        else:
                            raise e
                else:
                    # Task is running successfully
                    logger.info("✅ Position monitoring started successfully")
                    self._monitor_consecutive_errors = 0

                    # Start health check task
                    asyncio.create_task(self._monitor_health_checker())
                    return

            except Exception as e:
                logger.error(f"Error starting position monitoring (attempt {self._monitor_startup_attempts}): {e}")
                if self._monitor_startup_attempts < self._max_monitor_startup_attempts:
                    logger.info(f"Retrying position monitoring startup in {self._monitor_restart_delay} seconds...")
                    await asyncio.sleep(self._monitor_restart_delay)
                else:
                    logger.critical("❌ CRITICAL: Failed to start position monitoring after all attempts!")
                    # Don't raise exception - continue without position monitoring but log critical error
                    try:
                        error_msg = f"🚨 <b>CRITICAL ERROR</b>\n\nPosition monitoring failed to start after {self._max_monitor_startup_attempts} attempts.\n\nManual intervention required!"
                        if hasattr(self, 'signal_handler') and self.signal_handler:
                            asyncio.create_task(self.signal_handler.send_info_message(error_msg))
                    except Exception:
                        pass
                    return

    async def _monitor_health_checker(self):
        """Background task to monitor the health of position monitoring and restart if needed."""
        logger.info("Starting position monitoring health checker")

        while not self._stop_event.is_set():
            try:
                await asyncio.sleep(self._monitor_health_check_interval)

                # Check if monitor task is still running
                if not self._monitor_task or self._monitor_task.done():
                    logger.warning("🔄 Position monitoring task is not running - attempting restart")
                    await self._start_position_monitoring_resilient()
                    continue

                # Check for consecutive errors
                if self._monitor_consecutive_errors >= self._max_monitor_consecutive_errors:
                    logger.warning(f"🔄 Position monitoring has {self._monitor_consecutive_errors} consecutive errors - restarting")
                    await self._start_position_monitoring_resilient()
                    continue

                # Log health status
                open_positions_count = len(self.state.get_open_positions())
                logger.debug(f"💚 Position monitoring health check: OK (monitoring {open_positions_count} positions)")

            except asyncio.CancelledError:
                logger.info("Position monitoring health checker cancelled")
                break
            except Exception as e:
                logger.error(f"Error in position monitoring health checker: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _monitor_positions_resilient(self):
        """Resilient wrapper around the main position monitoring loop."""
        try:
            await self._monitor_positions()
        except asyncio.CancelledError:
            logger.info("Position monitoring cancelled")
            raise
        except Exception as e:
            self._monitor_consecutive_errors += 1
            logger.error(f"Position monitoring error (consecutive errors: {self._monitor_consecutive_errors}): {e}")

            # Send error notification
            try:
                error_msg = f"⚠️ <b>Position Monitoring Error</b>\n\nError: {str(e)}\nConsecutive errors: {self._monitor_consecutive_errors}\n\nThe system will attempt to restart monitoring."
                if hasattr(self, 'signal_handler') and self.signal_handler:
                    asyncio.create_task(self.signal_handler.send_info_message(error_msg))
            except Exception:
                pass

            # If too many consecutive errors, let the health checker handle restart
            if self._monitor_consecutive_errors >= self._max_monitor_consecutive_errors:
                logger.critical(f"Too many consecutive position monitoring errors ({self._monitor_consecutive_errors})")
                return

            # Otherwise, wait and restart
            await asyncio.sleep(self._monitor_restart_delay)
            await self._monitor_positions_resilient()

    async def _get_fresh_position_data(self, token_address: str) -> dict:
        """POSITION MONITORING: Get fresh position data using dedicated monitoring function."""
        try:
            # BULLETPROOF: Use dedicated position monitoring function - NOT analysis function
            if self.position_monitoring_function and callable(self.position_monitoring_function):
                result = await asyncio.wait_for(
                    self.position_monitoring_function(token_address),
                    timeout=1.5  # Faster timeout for position monitoring
                )

                if result and result.get('exists', False):
                    logger.debug(f"POSITION MONITORING: {token_address} - Price=${result.get('price', 0):.8f}, Liquidity=${result.get('liquidity_usd', 0):,.0f}")
                    return result
                else:
                    logger.warning(f"POSITION MONITORING: No data for {token_address}")
                    # Add to failed tokens if monitoring fails
                    self.failed_tokens[token_address] = time.time()
                    return {}
            else:
                # CRITICAL FIX: Fallback to fast_analysis_function if position monitoring not available
                logger.warning(f"POSITION MONITORING: Monitoring function not available, using fallback analysis for {token_address}")
                if self.fast_analysis_function and callable(self.fast_analysis_function):
                    try:
                        result = await asyncio.wait_for(
                            self.fast_analysis_function(token_address, force_fresh=True, skip_notifications=True),
                            timeout=2.0
                        )
                        if result and result.get('exists', False):
                            logger.info(f"POSITION MONITORING FALLBACK: Got data for {token_address} via fast analysis")
                            return result
                    except Exception as fallback_error:
                        logger.warning(f"POSITION MONITORING FALLBACK: Failed for {token_address}: {fallback_error}")

                # Final fallback: Use token analyzer
                logger.warning(f"POSITION MONITORING: Using token analyzer fallback for {token_address}")
                try:
                    result = await self.token_analyzer.analyze(token_address, force_fresh=True)
                    if result and result.get('exists', False):
                        logger.info(f"POSITION MONITORING TOKEN ANALYZER: Got data for {token_address}")
                        return result
                except Exception as analyzer_error:
                    logger.error(f"POSITION MONITORING TOKEN ANALYZER: Failed for {token_address}: {analyzer_error}")

                return {}

        except asyncio.TimeoutError:
            logger.warning(f"POSITION MONITORING: Timeout for {token_address}")
            # Add to failed tokens on timeout
            self.failed_tokens[token_address] = time.time()
            return {}
        except Exception as e:
            logger.warning(f"POSITION MONITORING: Error for {token_address}: {e}")
            # Add to failed tokens on error
            self.failed_tokens[token_address] = time.time()
            return {}

    async def _get_fresh_price_data(self, token_address: str) -> dict:
        """Get fresh price data for pre-execution validation using position monitoring."""
        try:
            # BULLETPROOF: Use position monitoring function for price validation
            if self.position_monitoring_function and callable(self.position_monitoring_function):
                result = await asyncio.wait_for(
                    self.position_monitoring_function(token_address),
                    timeout=1.0  # Fast timeout for price validation
                )
                return result if result else {}
            else:
                logger.warning(f"Position monitoring function not available for validation of {token_address}")
                return {}

        except asyncio.TimeoutError:
            logger.warning(f"Position monitoring timeout during validation for {token_address}")
            return {}
        except Exception as e:
            logger.warning(f"Error getting fresh price data for {token_address}: {e}")
            return {}





    async def cleanup(self):
        """Clean up resources before shutdown in a safe manner that prevents session corruption"""
        logger.info("Cleaning up bot resources...")
        print("Cleaning up bot resources...")

        # First, make sure we stop trading
        self.is_trading = False

        # Cancel any running background tasks
        try:
            # Find all tasks that might be related to our bot
            for task in asyncio.all_tasks():
                task_name = task.get_name()
                # Only cancel tasks that are clearly part of our bot
                # This avoids cancelling system tasks
                if any(name in task_name for name in ['signal', 'monitor', 'analysis', 'execution', 'websocket']):
                    if not task.done() and not task.cancelled():
                        logger.info(f"Cancelling task: {task_name}")
                        task.cancel()
        except Exception as e:
            logger.error(f"Error cancelling background tasks: {e}")

        # Stop parallel pipeline components
        try:
            if hasattr(self, 'analysis_worker_pool') and self.analysis_worker_pool:
                logger.info("Stopping analysis worker pool...")
                await self.analysis_worker_pool.stop()
                logger.info("Analysis worker pool stopped successfully")
                print("Analysis worker pool stopped")
        except Exception as e:
            logger.error(f"Error stopping analysis worker pool: {e}")
            print(f"Error stopping analysis worker pool: {e}")

        try:
            if hasattr(self, 'execution_queue') and self.execution_queue:
                logger.info("Stopping execution queue...")
                await self.execution_queue.stop()
                logger.info("Execution queue stopped successfully")
                print("Execution queue stopped")
        except Exception as e:
            logger.error(f"Error stopping execution queue: {e}")
            print(f"Error stopping execution queue: {e}")

        # WebSocket manager cleanup
        try:
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                logger.info("Disconnecting WebSocket manager...")
                await self.websocket_manager.disconnect()
                logger.info("WebSocket manager disconnected successfully")
                print("WebSocket manager disconnected")
        except Exception as e:
            logger.error(f"Error during WebSocket manager cleanup: {e}")
            print(f"Error during WebSocket manager cleanup: {e}")

        # Trading API cleanup
        try:
            if hasattr(self, 'token_analyzer') and self.token_analyzer:
                logger.info("Cleaning up token analyzer...")
                # Close sessions first to prevent unclosed session warnings
                await self.token_analyzer.close_sessions()
                # Wait for the cache to be cleared
                if hasattr(self.token_analyzer, 'clear_cache'):
                    await self.token_analyzer.clear_cache()
                    logger.info("Token analyzer sessions closed and cache cleared successfully")
                else:
                    logger.debug("Token analyzer has no clear_cache method")
                print("Token analyzer cache cleared")
        except Exception as e:
            logger.error(f"Error during token analyzer cleanup: {e}")
            print(f"Error during token analyzer cleanup: {e}")

        # Signal handler cleanup - Stop listening first
        try:
            if hasattr(self, 'signal_handler') and self.signal_handler:
                logger.info("Stopping signal handler...")
                # First stop listening - this prevents new messages from coming in
                await self.signal_handler.stop_listening()
                logger.info("Signal handler stopped listening")
                print("Signal handler stopped listening")

                # Then run the signal handler's internal cleanup
                await self.signal_handler.cleanup()
                logger.info("Signal handler cleanup completed")
                print("Signal handler cleanup completed")
        except Exception as e:
            logger.error(f"Error during signal handler cleanup: {e}")
            print(f"Error during signal handler cleanup: {e}")

        # Add a brief delay to ensure all message processing is complete
        await asyncio.sleep(1)

        # NOW handle the Telegram client disconnection as a separate, final step
        # This should be AFTER signal handler cleanup
        try:
            if hasattr(self, 'signal_handler') and self.signal_handler and self.signal_handler.client:
                logger.info("Handling Telegram client disconnection...")

                # Save the session first if possible
                if hasattr(self.signal_handler.client.session, 'save'):
                    try:
                        session_str = self.signal_handler.client.session.save()
                        if session_str and isinstance(session_str, str):
                            self.signal_handler._save_session_string(session_str)
                            logger.info("Final session string saved before disconnection")
                            print("Telegram session saved")
                    except Exception as se:
                        logger.error(f"Error saving final session: {se}")
                        print(f"Error saving Telegram session: {se}")

                # Now disconnect
                try:
                    if self.signal_handler.client.is_connected:
                        # Safe disconnect - this should not corrupt the session
                        await self.signal_handler.client.disconnect()
                        logger.info("Telegram client disconnected successfully")
                        print("Telegram client disconnected")
                except Exception as e:
                    logger.error(f"Error disconnecting Telegram client: {e}")
                    print(f"Error disconnecting Telegram client: {e}")

                # Wait a moment to ensure clean disconnect
                await asyncio.sleep(0.5)
        except Exception as e:
            logger.error(f"Error during Telegram client cleanup: {e}")
            print(f"Error during Telegram client cleanup: {e}")

        # Real trading only - no simulation components

        # Save state as the final step
        try:
            self.state.save_state()
            logger.info("State saved successfully")
            print("State saved successfully")
        except Exception as e:
            logger.error(f"Error saving state: {e}")
            print(f"Error saving state: {e}")

        logger.info("Cleanup completed")
        print("Cleanup completed")
        return True

    # --- Helper methods for status display ---
    def get_current_strategy(self) -> str:
        """Get the current trading strategy."""
        return self.current_strategy

    def get_starting_sol(self) -> float:
        """Get the starting SOL amount."""
        return self.state.starting_sol

    def get_sol_per_trade(self) -> float:
        """Get the SOL per trade amount."""
        return self.sol_per_trade

    def get_available_sol(self) -> float:
        """Get the available SOL amount."""
        return self.state.available_sol

    def get_positions(self) -> dict:
        """Get all open positions."""
        return self.state.get_open_positions()

    def get_total_trades(self) -> int:
        """Get the total number of trades."""
        # FIXED: Use consistent counters - win_count + loss_count is the primary total
        return self.state.win_count + self.state.loss_count

    def get_win_rate(self) -> float:
        """Get the win rate percentage."""
        # FIXED: Use consistent counters - win_count and loss_count are the primary counters
        total_trades = self.state.win_count + self.state.loss_count
        if total_trades == 0:
            return 0.0
        return (self.state.win_count / total_trades) * 100

    def get_total_profit(self) -> float:
        """Get the total profit in SOL."""
        return self.state.total_profit

    def is_telegram_connected(self) -> bool:
        """Check if Telegram is connected."""
        return self.signal_handler.is_connected()

    def get_signal_queue_size(self) -> int:
        """Get the size of the signal queue."""
        return self.signal_handler.get_queue_size()

    def get_active_agents_count(self) -> int:
        """Get the number of active trading agents."""
        # This is a placeholder - implement actual logic based on your system
        return len(self.get_positions())

    def get_max_agents(self) -> int:
        """Get the maximum number of allowed trading agents."""
        return self.max_concurrent_trades

    def get_analysis_worker_stats(self) -> dict:
        """Get statistics from the analysis worker pool."""
        if self.analysis_worker_pool:
            return self.analysis_worker_pool.stats
        return {}

    def get_execution_queue_stats(self) -> dict:
        """Get statistics from the execution queue."""
        if self.execution_queue:
            return self.execution_queue.stats
        return {}

    def get_pipeline_status(self) -> dict:
        """Get status information about the parallel pipeline."""
        status = {
            "analysis_workers": self.num_analysis_workers if self.analysis_worker_pool else 0,
            "execution_workers": self.num_execution_workers if self.execution_queue else 0,
            "analysis_queue_size": self.analysis_worker_pool.analysis_queue.qsize() if self.analysis_worker_pool else 0,
            "buy_queue_size": self.execution_queue.buy_queue.qsize() if self.execution_queue else 0,
            "sell_queue_size": self.execution_queue.sell_queue.qsize() if self.execution_queue else 0,
            "analysis_worker_running": self.analysis_worker_pool.is_running if self.analysis_worker_pool else False,
            "execution_queue_running": self.execution_queue.is_running if self.execution_queue else False
        }
        return status

    # --- CLI Passthrough Methods (Simplified) ---
    async def view_positions(self):
        open_positions = self.state.get_open_positions()
        print(f"\n--- Open Positions ({len(open_positions)}) ---")
        if not open_positions:
            print("No open positions.")
            return
        for token, data in open_positions.items():
            print(f"  Token: {token}")
            print(f"    Entry Price: {data.get('entry_price', 0):.6f}")
            print(f"    Token Amount: {data.get('token_amount', 0):.4f}")
            print(f"    Initial SOL: {data.get('sol_amount', 0):.4f}")
            print(f"    Highest Price: {data.get('highest_price', 0):.6f}")
            print(f"    Partial Taken: {data.get('partial_taken', False)}")
            print(f"    Strategy: {data.get('strategy', 'N/A')}")
        print("--------------------")

    async def view_moonbags(self):
        # Moonbag feature has been removed as requested
        print("\n--- Moonbags ---")
        print("Moonbag feature has been removed as requested.")
        print("--------------------")

    async def sell_all_positions(self):
        """Sell all open positions."""
        # Ensure trading is active to have initialized GmgnTrader if in REAL mode
        if not self.is_trading and self.run_mode == "REAL":
            print("Error: Trading is not active. Cannot execute real sells.")
            print("Start trading first (e.g., 'start real').")
            return

        open_positions = self.state.get_open_positions()
        if not open_positions:
            print("No open positions to sell.")
            return

        print(f"Attempting to sell {len(open_positions)} open positions...")
        # Real trading only - simulation mode removed
        sold_count = 0
        failed_count = 0

        # Create a copy of keys to avoid issues if dict changes during iteration
        position_keys = list(open_positions.keys())

        for token_address in position_keys:
            try:
                # Check position still exists and is open
                position_data = self.state.get_position(token_address)
                if not position_data or position_data.get('status') != 'OPEN':
                    logger.info(f"Skipping sell for {token_address}, already closed or removed.")
                    continue

                logger.info(f"Attempting manual sell for {token_address}...")
                # Fetch current price for logging/state update using fast analysis
                analysis = None
                if self.fast_analysis_function and callable(self.fast_analysis_function):
                    analysis = await self.fast_analysis_function(token_address)

                # Check if token exists in DexScreener
                if not analysis or not analysis.get("exists", False):
                    logger.warning(f"Token {token_address} not found in DexScreener. Using last known price for state update.")
                    current_price = position_data.get('highest_price', position_data.get('entry_price', 0))
                else:
                    current_price = analysis.get("price", 0)

                # Fallback if price is still 0
                if current_price <= 0:
                    logger.warning(f"Could not get valid current price for {token_address}. Using last known highest price for state update.")
                    current_price = position_data.get('highest_price', position_data.get('entry_price', 0))

                entry_price = position_data.get('entry_price', 0)
                token_amount = position_data.get('token_amount', 0)

                if token_amount <= 0:
                    logger.warning(f"Position {token_address} has zero token amount. Skipping sell.")
                    failed_count += 1
                    continue

                # Use strategy-specific slippage from finalconfig.json with adaptive support
                # Get current market data for adaptive slippage
                current_market_data = {
                    'liquidity_usd': position_data.get('liquidity_usd', 0),
                    'volume_5m': position_data.get('volume_5m', 0),
                    'volume_24h': position_data.get('volume_24h', 0),
                    'market_cap': position_data.get('market_cap', 0)
                }
                strategy_slippage = self.get_strategy_slippage(token_address, current_market_data)
                sell_slippage_bps = int(strategy_slippage * 10000)

                logger.info(f"Using strategy slippage for manual sell {token_address}: {strategy_slippage:.4f} ({sell_slippage_bps} bps)")

                # Execute sell (NOW ASYNC)
                sell_result = await self.trade_executor.execute_sell( # <= Added await
                    token_address=token_address,
                    token_amount=token_amount,
                    entry_price=entry_price,
                    slippage_bps=sell_slippage_bps,
                    sell_fraction=1.0 # Sell full amount for sell_all
                )

                sell_success = sell_result.get('success', False) if isinstance(sell_result, dict) else False
                sell_verified = sell_result.get('verified', False) if isinstance(sell_result, dict) else False

                if sell_success and sell_verified:
                    logger.info(f"✅ Manual sell VERIFIED successful for {token_address}")
                    # Update StateManager
                    self.state.close_position(token_address, current_price, "manual_sell_all", 1.0) # Close 100%
                    sold_count += 1
                elif sell_success and not sell_verified:
                    logger.error(f"❌ Manual sell sent but FAILED verification for {token_address}")
                    logger.error(f"❌ Transaction may have failed: {sell_result.get('transaction_signature', 'N/A')}")
                    failed_count += 1
                else:
                    logger.error(f"Manual sell command FAILED for {token_address}.")
                    failed_count += 1
            except Exception as e:
                logger.error(f"Error during sell_all for {token_address}: {e}", exc_info=True)
                failed_count += 1

        print(f"Sell all complete. Sent sell commands for: {sold_count}, Failed/Skipped: {failed_count}")

        # Send balance update after sell_all if any positions were sold
        if sold_count > 0:
            try:
                await self.send_balance_update(reason="After Sell All")
            except Exception as e:
                logger.error(f"Error sending balance update after sell_all: {e}")

    async def emergency_sell_rugged(self):
        """SURGICAL FIX: Emergency sell rugged positions with liquidity < $1000."""
        if not self.is_trading and self.run_mode == "REAL":
            print("Error: Trading is not active. Cannot execute real sells.")
            print("Start trading first (e.g., 'start real').")
            return

        open_positions = self.state.get_open_positions()
        if not open_positions:
            print("No open positions to check.")
            return

        print("🔍 Checking positions for rug detection...")
        rugged_count = 0

        for token_address, _ in open_positions.items():
            try:
                # Get current liquidity using fast analysis
                analysis = None
                if self.fast_analysis_function and callable(self.fast_analysis_function):
                    analysis = await self.fast_analysis_function(token_address)

                current_liquidity = analysis.get("liquidity_usd", 0) if analysis else 0

                # Check if rugged (liquidity < $1000)
                if current_liquidity < 1000:
                    rugged_count += 1
                    print(f"🚨 RUGGED TOKEN DETECTED: {token_address}")
                    print(f"   Current liquidity: ${current_liquidity:.0f}")
                    print(f"   Placing emergency sell order...")

                    # PERMANENT FIX: Ensure execution queue is running for emergency sells
                    if not self.execution_queue.is_running:
                        logger.warning(f"Execution queue not running, starting it now for emergency sell {token_address}")
                        try:
                            await self.execution_queue.start()
                            logger.info("✅ Execution queue started for emergency sell")
                        except Exception as e:
                            logger.error(f"❌ CRITICAL: Failed to start execution queue for emergency sell: {e}")
                            continue

                    # Force immediate sell using execution queue
                    position_data = self.state.positions.get(token_address, {})
                    await self.execution_queue.queue_sell(
                        token_address=token_address,
                        token_amount=position_data.get('token_amount', 0),
                        entry_price=position_data.get('entry_price', 0),
                        slippage_bps=1000,  # 10% emergency slippage
                        sell_fraction=1.0,
                        priority=0  # SURGICAL FIX: Emergency priority
                    )
                    print(f"✅ Emergency sell order placed for {token_address}")
                else:
                    print(f"✅ {token_address}: Healthy (${current_liquidity:.0f} liquidity)")

            except Exception as e:
                print(f"❌ Failed to check {token_address}: {e}")

        if rugged_count > 0:
            print(f"\n🚨 Found {rugged_count} rugged positions - emergency sell orders placed!")
        else:
            print("\n✅ No rugged positions detected.")

        print("Check logs for execution status.")

    async def reload_config(self):
        """SURGICAL FIX: Reload configuration from finalconfig.json without restarting bot"""
        print("🔄 Reloading configuration from finalconfig.json...")

        try:
            # Reload the config manager
            from config_manager import ConfigManager
            self.config = ConfigManager()

            # Reload rug protection settings
            trading_settings_reload = self.config.get_section('trading_settings')
            rug_protection_settings = trading_settings_reload.get('rug_protection', {})
            old_min_liquidity = self.min_liquidity_usd
            self.min_liquidity_usd = rug_protection_settings.get('min_liquidity_usd', 8000.0)  # SURGICAL FIX: Correct path

            # Reload trading settings
            self.trade_settings = self.config.get_section('trading_settings')
            old_max_concurrent = self.max_concurrent_trades
            old_max_hold_time = self.max_hold_time_minutes
            self.max_concurrent_trades = self.trade_settings.get('max_concurrent_trades', 1)  # Fixed default to 1
            self.max_hold_time_minutes = self.trade_settings.get('max_hold_time_minutes', 3)  # Fixed default to 3

            # SURGICAL FIX: Restart position monitoring if critical settings changed
            if (old_max_concurrent != self.max_concurrent_trades or
                old_max_hold_time != self.max_hold_time_minutes):
                logger.info(f"🔄 Position monitoring settings changed - restarting monitor...")
                try:
                    # Cancel existing monitor task
                    if hasattr(self, '_monitor_task') and self._monitor_task and not self._monitor_task.done():
                        self._monitor_task.cancel()
                        try:
                            await self._monitor_task
                        except asyncio.CancelledError:
                            pass

                    # Restart position monitoring with new settings
                    await self._start_position_monitoring_resilient()
                    logger.info("✅ Position monitoring restarted with new settings")
                except Exception as e:
                    logger.error(f"Error restarting position monitoring: {e}")

            # Reload current strategy settings
            if hasattr(self, 'current_strategy') and self.current_strategy:
                strategy_name = self.current_strategy.upper()
                strategies = self.config.get_section('trading_settings').get('strategies', {})
                strategy_params = strategies.get(strategy_name, {})

                if strategy_params:
                    # Update TP/SL settings from strategy
                    if 'take_profit_percent' in strategy_params:
                        take_profit = strategy_params.get('take_profit_percent')
                        self.tp_sl_settings['pnl_sell_percent_min'] = take_profit
                        self.tp_sl_settings['pnl_sell_percent_max'] = take_profit

                    if 'stop_loss_percent' in strategy_params:
                        stop_loss = strategy_params.get('stop_loss_percent')
                        self.tp_sl_settings['stop_loss_percent'] = stop_loss

                    if 'min_confidence' in strategy_params:
                        self.min_confidence = strategy_params.get('min_confidence')

            print("✅ Configuration reloaded successfully!")
            print(f"📊 Strategy: {self.current_strategy}")
            print(f"💧 Min Liquidity: ${self.min_liquidity_usd:,.0f} (was ${old_min_liquidity:,.0f})")
            print(f"🎯 Take Profit: {self.tp_sl_settings.get('pnl_sell_percent_min')}%")
            print(f"🛑 Stop Loss: {self.tp_sl_settings.get('stop_loss_percent')}%")
            print(f"🔢 Max Concurrent Trades: {self.max_concurrent_trades}")
            print(f"⏱️ Max Hold Time: {self.max_hold_time_minutes} minutes")

            logger.info(f"Configuration reloaded: min_liquidity=${self.min_liquidity_usd}, strategy={self.current_strategy}")

        except Exception as e:
            print(f"❌ Error reloading configuration: {e}")
            logger.error(f"Error reloading configuration: {e}")

    async def set_strategy(self, strategy: str):
        """Change trading strategy and adjust TP/SL parameters accordingly."""
        valid_strategies = ['SAFE', 'AGGRESSIVE', 'ULTRA_CONSERVATIVE', 'CUSTOM', 'DEFAULT', 'CONSERVATIVE']
        if strategy not in valid_strategies:
            logger.error(f"Invalid strategy: {strategy}. Must be one of {valid_strategies}")
            print(f"Invalid strategy: {strategy}. Must be one of {valid_strategies}")
            return False

        self.current_strategy = strategy
        print(f"Strategy set to: {strategy}")

        # First try to load from finalconfig.json
        json_strategy = strategy.upper()
        strategy_params = None

        # Get strategy parameters from finalconfig.json
        strategies = self.config.get_section('trading_settings').get('strategies', {})
        strategy_params = strategies.get(json_strategy, None)

        if strategy_params:
            logger.info(f"Loading strategy parameters from finalconfig.json for {json_strategy} strategy")

            # Apply simplified strategy parameters from finalconfig.json
            # Set take profit percentage
            if 'take_profit_percent' in strategy_params:
                take_profit = strategy_params.get('take_profit_percent')
                self.tp_sl_settings['pnl_sell_percent_min'] = take_profit
                self.tp_sl_settings['pnl_sell_percent_max'] = take_profit
                logger.info(f"Set take profit to {take_profit}%")

            # Set stop loss percentage
            if 'stop_loss_percent' in strategy_params:
                stop_loss = strategy_params.get('stop_loss_percent')
                self.tp_sl_settings['stop_loss_percent'] = stop_loss
                logger.info(f"Set stop loss to {stop_loss}%")

            # Set minimum confidence threshold
            if 'min_confidence' in strategy_params:
                self.min_confidence = strategy_params.get('min_confidence')
                logger.info(f"Set minimum confidence to {self.min_confidence}")


            logger.info("✅ CENTRALIZED RUG PROTECTION: Using values from rug_protection section only")

            print(f"Applied {json_strategy} strategy settings from finalconfig.json")
            print(f"Take profit: {self.tp_sl_settings.get('pnl_sell_percent_min')}%, Stop loss: {self.tp_sl_settings.get('stop_loss_percent')}%")
            print(f"Min confidence: {self.min_confidence}, Min liquidity: ${self.min_liquidity_usd}")
            logger.info(f"Applied {json_strategy} strategy with parameters: {strategy_params}")

        # If strategy not found in finalconfig.json, fall back to simplified hardcoded values
        else:
            logger.warning(f"Strategy {json_strategy} not found in finalconfig.json, using hardcoded values")
            self._apply_simplified_strategy(strategy)

        # Log the new settings
        logger.info(f"Strategy changed to {strategy} with settings: {self.tp_sl_settings}")

        # Save strategy to state
        self.state.current_strategy = strategy
        self.state.save_state()

        return True

    def get_strategy_slippage(self, token_address=None, market_data=None):
        """
        Get slippage percentage from strategy configuration with adaptive slippage support.

        Args:
            token_address: Token address for logging (optional)
            market_data: Market data for adaptive slippage calculation (optional)

        Returns:
            float: Slippage as a decimal (e.g., 0.12 for 12%)
        """
        try:
            # Get current strategy parameters
            strategy_name = self.current_strategy.upper() if hasattr(self, 'current_strategy') and self.current_strategy else "AGGRESSIVE"
            trading_settings = self.config.get_section('trading_settings')
            strategies = trading_settings.get('strategies', {})
            strategy_params = strategies.get(strategy_name, {})

            # Get base slippage from strategy
            base_slippage_percent = strategy_params.get('slippage_buffer_percent', 8.0)  # Default 8%

            # SURGICAL FIX: Check if adaptive slippage is enabled in transaction_settings (nested under trading_settings)
            trading_settings = self.config.get_section('trading_settings')
            transaction_settings = trading_settings.get('transaction_settings', {})
            use_adaptive_slippage = transaction_settings.get('use_adaptive_slippage', False)

            logger.debug(f"Adaptive slippage setting: {use_adaptive_slippage} (from transaction_settings.use_adaptive_slippage)")

            if use_adaptive_slippage:
                # Calculate adaptive slippage based on market conditions
                # If no market data provided, use empty dict (will use base slippage with minimal adjustments)
                market_data_safe = market_data if market_data else {}
                adaptive_slippage = self._calculate_adaptive_slippage(base_slippage_percent, market_data_safe, token_address)
                logger.info(f"✅ Using ADAPTIVE slippage for {token_address or 'trade'}: {adaptive_slippage:.1f}% (base: {base_slippage_percent}%) from {strategy_name} strategy")
                print(f"🎯 ADAPTIVE SLIPPAGE: {adaptive_slippage:.1f}% (base: {base_slippage_percent}%) for {token_address or 'trade'}")
                return adaptive_slippage / 100.0
            else:
                # Use base strategy slippage
                logger.info(f"Using base strategy slippage for {token_address or 'trade'}: {base_slippage_percent}% from {strategy_name} strategy")
                print(f"🎯 FIXED SLIPPAGE: {base_slippage_percent}% from {strategy_name} strategy for {token_address or 'trade'}")
                return base_slippage_percent / 100.0

        except Exception as e:
            logger.warning(f"Error getting strategy slippage, using default 8%: {e}")
            return 0.08  # Safe default 8%

    def _calculate_adaptive_slippage(self, base_slippage_percent: float, market_data: dict, token_address: str = None) -> float:
        """
        Calculate adaptive slippage based on market conditions.

        Args:
            base_slippage_percent: Base slippage from strategy
            market_data: Market data containing liquidity, volume, etc.
            token_address: Token address for logging

        Returns:
            float: Adaptive slippage percentage
        """
        try:
            # Extract market metrics
            liquidity_usd = market_data.get('liquidity_usd', 0)
            volume_5m = market_data.get('volume_5m', 0)
            # volume_24h = market_data.get('volume_24h', 0)  # Not used in current logic
            market_cap = market_data.get('market_cap', 0)

            # Start with base slippage
            adaptive_slippage = base_slippage_percent

            # Liquidity factor - lower liquidity = higher slippage
            if liquidity_usd < 5000:
                adaptive_slippage += 5.0  # Add 5% for very low liquidity
                logger.debug(f"Low liquidity ({liquidity_usd:.0f}): +5% slippage")
            elif liquidity_usd < 15000:
                adaptive_slippage += 3.0  # Add 3% for low liquidity
                logger.debug(f"Medium liquidity ({liquidity_usd:.0f}): +3% slippage")
            elif liquidity_usd > 50000:
                adaptive_slippage -= 2.0  # Reduce 2% for high liquidity
                logger.debug(f"High liquidity ({liquidity_usd:.0f}): -2% slippage")

            # Volume factor - low volume = higher slippage
            if volume_5m < 500:
                adaptive_slippage += 3.0  # Add 3% for very low volume
                logger.debug(f"Low 5m volume ({volume_5m:.0f}): +3% slippage")
            elif volume_5m > 5000:
                adaptive_slippage -= 1.0  # Reduce 1% for high volume
                logger.debug(f"High 5m volume ({volume_5m:.0f}): -1% slippage")

            # Market cap factor - very small caps need higher slippage
            if market_cap < 10000:
                adaptive_slippage += 4.0  # Add 4% for micro caps
                logger.debug(f"Micro cap ({market_cap:.0f}): +4% slippage")
            elif market_cap < 50000:
                adaptive_slippage += 2.0  # Add 2% for small caps
                logger.debug(f"Small cap ({market_cap:.0f}): +2% slippage")

            # Ensure slippage stays within reasonable bounds
            min_slippage = base_slippage_percent * 0.5  # At least 50% of base
            max_slippage = base_slippage_percent * 3.0  # At most 300% of base
            adaptive_slippage = max(min_slippage, min(max_slippage, adaptive_slippage))

            logger.info(f"Adaptive slippage for {token_address}: {adaptive_slippage:.1f}% (base: {base_slippage_percent}%)")
            return adaptive_slippage

        except Exception as e:
            logger.error(f"Error calculating adaptive slippage: {e}")
            return base_slippage_percent



    def get_adaptive_tp_sl(self, strategy_params: dict, confidence: float = 0.5, liquidity_usd: float = 0, volume_5m_usd: float = 0, token_address: str = None) -> tuple:
        """
        Get adaptive take-profit and stop-loss based on strategy parameters and market conditions.
        FIXED: Ensures stop losses don't become too tight and respects minimum thresholds.

        Args:
            strategy_params: Strategy configuration from finalconfig.json
            confidence: Signal confidence (0.0 to 1.0)
            liquidity_usd: Token liquidity in USD
            volume_5m_usd: 5-minute volume in USD
            token_address: Token address for logging (optional)

        Returns:
            tuple: (take_profit_percent, stop_loss_percent)
        """
        use_adaptive = strategy_params.get('use_adaptive_tp_sl', False)

        if not use_adaptive:
            # Use fixed values from strategy configuration
            tp = strategy_params.get('take_profit_percent', 15.0)
            sl = strategy_params.get('stop_loss_percent', -30.0)
            logger.debug(f"Using fixed TP/SL for {token_address}: TP={tp}%, SL={sl}%")
            return tp, sl

        # Adaptive TP/SL calculation
        base_tp = strategy_params.get('take_profit_percent', 20.0)
        base_sl = strategy_params.get('stop_loss_percent', -40.0)

        tp_range = strategy_params.get('take_profit_percent_range', [10, 30])
        sl_range = strategy_params.get('stop_loss_percent_range', [-40, -20])

        tp_min, tp_max = tp_range[0], tp_range[1]
        sl_min, sl_max = sl_range[0], sl_range[1]  # sl_min is more negative, sl_max is less negative

        # CRITICAL FIX: Ensure stop loss never becomes tighter than the base strategy stop loss
        # This prevents the adaptive system from making stop losses too aggressive
        sl_max = min(sl_max, base_sl)  # Don't allow SL to be less negative than base

        # Calculate adaptive multipliers based on market conditions
        confidence_factor = self._calculate_confidence_factor(confidence)
        liquidity_factor = self._calculate_liquidity_factor(liquidity_usd)
        volume_factor = self._calculate_volume_factor(volume_5m_usd, liquidity_usd)

        # IMPROVED: More conservative weighting for low confidence signals
        # Reduce the impact of adaptive factors for low confidence signals
        if confidence < 0.6:
            # For low confidence signals, use more conservative (wider) stop losses
            adaptive_factor = min(0.3, (confidence_factor * 0.4) + (liquidity_factor * 0.2) + (volume_factor * 0.1))
            logger.info(f"Low confidence signal ({confidence:.2f}) - using conservative adaptive factor: {adaptive_factor:.2f}")
        else:
            # Normal weighting for higher confidence signals
            adaptive_factor = (confidence_factor * 0.5) + (liquidity_factor * 0.3) + (volume_factor * 0.2)

        # Calculate adaptive TP (higher confidence/liquidity = higher TP)
        tp_adaptive = tp_min + (adaptive_factor * (tp_max - tp_min))
        tp_adaptive = max(tp_min, min(tp_max, tp_adaptive))

        # Calculate adaptive SL (higher confidence/liquidity = tighter SL, less negative)
        # CRITICAL FIX: For low confidence or low liquidity, use wider (more negative) stop losses
        if confidence < 0.6 or liquidity_usd < 5000:
            # Use base stop loss or wider for risky signals
            sl_adaptive = min(base_sl, sl_min + (adaptive_factor * 0.5 * (sl_max - sl_min)))
            logger.info(f"Risky signal detected - using wider stop loss: {sl_adaptive:.1f}%")
        else:
            sl_adaptive = sl_min + (adaptive_factor * (sl_max - sl_min))

        # Ensure stop loss is never tighter than base strategy
        sl_adaptive = min(sl_adaptive, base_sl)
        sl_adaptive = max(sl_min, min(sl_max, sl_adaptive))

        logger.info(f"Adaptive TP/SL for {token_address}: TP={tp_adaptive:.1f}% (base: {base_tp}%), SL={sl_adaptive:.1f}% (base: {base_sl}%)")
        logger.debug(f"Factors - Confidence: {confidence_factor:.2f}, Liquidity: {liquidity_factor:.2f}, Volume: {volume_factor:.2f}, Combined: {adaptive_factor:.2f}")

        return round(tp_adaptive, 1), round(sl_adaptive, 1)

    def get_fixed_tp_sl(self, strategy_params: dict, token_address: str = None) -> tuple:
        """
        Backward compatibility method - now calls adaptive method with default values.
        """
        return self.get_adaptive_tp_sl(strategy_params, token_address=token_address)

    def _calculate_confidence_factor(self, confidence: float) -> float:
        """
        Calculate adaptive factor based on signal confidence.

        Args:
            confidence: Signal confidence (0.0 to 1.0)

        Returns:
            float: Factor between 0.0 and 1.0
        """
        # Normalize confidence to 0-1 range with sigmoid-like curve
        if confidence >= 0.8:
            return 1.0  # Very high confidence
        elif confidence >= 0.7:
            return 0.8  # High confidence
        elif confidence >= 0.6:
            return 0.6  # Medium-high confidence
        elif confidence >= 0.5:
            return 0.4  # Medium confidence
        elif confidence >= 0.4:
            return 0.2  # Low confidence
        else:
            return 0.0  # Very low confidence

    def _calculate_liquidity_factor(self, liquidity_usd: float) -> float:
        """
        Calculate adaptive factor based on token liquidity.

        Args:
            liquidity_usd: Token liquidity in USD

        Returns:
            float: Factor between 0.0 and 1.0
        """
        # Liquidity brackets for adaptive calculation
        if liquidity_usd >= 50000:
            return 1.0  # Very high liquidity
        elif liquidity_usd >= 25000:
            return 0.8  # High liquidity
        elif liquidity_usd >= 15000:
            return 0.6  # Medium-high liquidity
        elif liquidity_usd >= 10000:
            return 0.4  # Medium liquidity
        elif liquidity_usd >= 5000:
            return 0.2  # Low liquidity
        else:
            return 0.0  # Very low liquidity

    def _calculate_volume_factor(self, volume_5m_usd: float, liquidity_usd: float) -> float:
        """
        Calculate adaptive factor based on volume-to-liquidity ratio.

        Args:
            volume_5m_usd: 5-minute volume in USD
            liquidity_usd: Token liquidity in USD

        Returns:
            float: Factor between 0.0 and 1.0
        """
        if liquidity_usd <= 0:
            return 0.0

        # Calculate volume-to-liquidity ratio
        vlr = volume_5m_usd / liquidity_usd

        # Volume activity brackets
        if vlr >= 2.0:
            return 1.0  # Very high activity
        elif vlr >= 1.0:
            return 0.8  # High activity
        elif vlr >= 0.5:
            return 0.6  # Medium-high activity
        elif vlr >= 0.2:
            return 0.4  # Medium activity
        elif vlr >= 0.1:
            return 0.2  # Low activity
        else:
            return 0.0  # Very low activity

    def _apply_simplified_strategy(self, strategy: str):
        """Apply simplified strategy parameters with only take profit and stop loss."""
        if strategy == 'SAFE':
            # Simple SAFE strategy with 15% take profit and -30% stop loss
            self.tp_sl_settings['pnl_sell_percent_min'] = 15.0
            self.tp_sl_settings['pnl_sell_percent_max'] = 15.0
            self.tp_sl_settings['stop_loss_percent'] = -30.0

            # Set minimum confidence threshold
            self.min_confidence = 0.6

            # REMOVED: Hardcoded liquidity thresholds - now read from rug_protection section

            print("Applied SAFE strategy settings: 15% take profit, -30% stop loss")
            print(f"Min confidence: {self.min_confidence}, Min liquidity: ${self.min_liquidity_usd}")

        elif strategy == 'AGGRESSIVE':
            # Simple AGGRESSIVE strategy with 20% take profit and -40% stop loss
            self.tp_sl_settings['pnl_sell_percent_min'] = 20.0
            self.tp_sl_settings['pnl_sell_percent_max'] = 20.0
            self.tp_sl_settings['stop_loss_percent'] = -40.0

            # Set minimum confidence threshold
            self.min_confidence = 0.5



            print("Applied AGGRESSIVE strategy settings: 20% take profit, -40% stop loss")
            print(f"Min confidence: {self.min_confidence}, Min liquidity: ${self.min_liquidity_usd}")

        elif strategy == 'ULTRA_CONSERVATIVE':
            # Simple ULTRA_CONSERVATIVE strategy with 10% take profit and -20% stop loss
            self.tp_sl_settings['pnl_sell_percent_min'] = 10.0
            self.tp_sl_settings['pnl_sell_percent_max'] = 10.0
            self.tp_sl_settings['stop_loss_percent'] = -20.0

            # Set minimum confidence threshold
            self.min_confidence = 0.7



            print("Applied ULTRA_CONSERVATIVE strategy settings: 10% take profit, -20% stop loss")
            print(f"Min confidence: {self.min_confidence}, Min liquidity: ${self.min_liquidity_usd}")

        elif strategy == 'CUSTOM':
            # Reload from config to ensure we have the latest values
            self.tp_sl_settings = self.config.get_tp_sl_settings()
            print("Applied CUSTOM strategy settings from config file")

    async def set_total_sol_interactive(self):
        """Set the total starting SOL capital for the session interactively."""
        try:
            current_start = self.state.starting_sol
            current_avail = self.state.available_sol
            print(f"Current Starting Capital: {current_start:.4f} SOL")
            print(f"Current Available SOL: {current_avail:.4f} SOL")
            amount_str = input("Enter new total starting SOL amount (resets PnL stats): ")
            new_amount = float(amount_str)
            if new_amount > 0:
                if self.state.reset_capital(new_amount):
                     print(f"Total starting SOL reset to: {new_amount:.4f}. Available SOL updated.")
                else:
                     print("Failed to reset capital in state manager.")
            else:
                print("Invalid amount. Must be positive.")
        except ValueError:
            print("Invalid input. Please enter a number.")
        except Exception as e:
             logger.error(f"Error setting total SOL: {e}", exc_info=True)
             print("An error occurred while setting total SOL.")

    async def set_sol_per_trade_interactive(self):
        """Set the SOL per trade amount interactively."""
        try:
            # Standard SOL amounts for trading
            buy_amounts = {
                'small': 0.05,
                'medium': 0.1,
                'large': 0.16,
                'xlarge': 0.25
            }

            # Extract supported amounts as a sorted list
            supported_amounts = []
            for key, value in buy_amounts.items():
                if isinstance(value, (int, float)) and value > 0:
                    supported_amounts.append(value)
            supported_amounts.sort()

            # Show current value and supported amounts
            print(f"\n=== SOL Per Trade Settings ===")
            print(f"Current SOL per trade: {self.sol_per_trade} SOL")

            # Show where the current value came from
            try:
                config_value = self.trade_settings.get('sol_trade_amount', 'not set')
                print(f"Config value (finalconfig.json): {config_value} SOL")
                if hasattr(self.state, 'sol_per_trade') and self.state.sol_per_trade > 0:
                    print(f"State value (user override): {self.state.sol_per_trade} SOL")
                    print("📝 Note: Using user override value (set via CLI)")
                else:
                    print("📝 Note: Using config value from finalconfig.json")
            except Exception as e:
                logger.debug(f"Error showing config source: {e}")

            # Check if we're in real mode (option 2)
            # BotController doesn't have a trading_mode attribute, so we need to check run_mode
            is_real_mode = hasattr(self, 'run_mode') and self.run_mode == "REAL"

            if supported_amounts:
                if is_real_mode:
                    print(f"\nGMGN Bot supported buy amounts (for Real Mode only):")
                    print(f"  {', '.join([f'{amount:.2f}' for amount in supported_amounts])} SOL")
                    print("\nNote: In Real Mode (option 2), if you enter a value that's not in this list,")
                    print("      the bot will use the closest lower supported amount when executing trades.")
                else:
                    print(f"\nNote: This is real trading mode - use realistic SOL amounts.")
                    print(f"      The GMGN Bot supported amounts only apply to Real Mode (option 2).")

            # Get new amount from user
            new_amount = float(input(f"\nEnter new SOL per trade: "))
            if new_amount <= 0:
                print("Invalid amount. Must be greater than zero.")
                return

            # Update runtime value
            self.sol_per_trade = new_amount

            # Save to state for persistence across restarts
            if hasattr(self.state, 'sol_per_trade'):
                self.state.sol_per_trade = new_amount
                self.state.save_state()
                logger.info(f"Saved SOL per trade setting to state: {new_amount} SOL")

            # Check if amount matches a supported GMGN amount
            if supported_amounts:
                closest_lower = None
                for amount in supported_amounts:
                    if amount <= new_amount:
                        closest_lower = amount
                    else:
                        break

                # Check if we're in real mode (option 2)
                # BotController doesn't have a trading_mode attribute, so we need to check run_mode
                is_real_mode = hasattr(self, 'run_mode') and self.run_mode == "REAL"

                if new_amount in supported_amounts:
                    if is_real_mode:
                        print(f"\nSOL per trade set to: {new_amount:.2f} SOL (Supported by GMGN bot)")
                    else:
                        print(f"\nSOL per trade set to: {new_amount:.2f} SOL")
                elif closest_lower is not None and is_real_mode:
                    print(f"\nSOL per trade set to: {new_amount:.2f} SOL")
                    print(f"Note: When executing trades in Real Mode, the bot will use {closest_lower:.2f} SOL")
                    print(f"      (the closest lower amount supported by GMGN bot)")
                elif is_real_mode:
                    print(f"\nSOL per trade set to: {new_amount:.2f} SOL")
                    print(f"Warning: This amount is lower than any supported GMGN bot amount.")
                    print(f"         The minimum supported amount is {min(supported_amounts):.2f} SOL.")
                else:
                    print(f"\nSOL per trade set to: {new_amount:.2f} SOL")
            else:
                print(f"\nSOL per trade set to: {new_amount:.2f} SOL")

            # Log the change
            logger.info(f"User changed SOL per trade to {new_amount} SOL via CLI")

        except ValueError:
            print("Invalid input. Please enter a valid number.")

    async def view_signal_status(self):
        """Display detailed signal status information similar to telegram_signal_monitor."""
        print("\n" + "=" * 80)
        print("SIGNAL STATUS AND DEBUG INFORMATION")
        print("=" * 80)

        # Get signal queue size
        signal_queue_size = self.get_signal_queue_size()
        print(f"Signal Queue Size: {signal_queue_size}")

        # Check if simulated signals are disabled
        print(f"Simulated Signals: {'DISABLED' if self.signal_handler.disable_simulated_signals else 'ENABLED'}")

        # Check Telegram connection status
        telegram_status = "CONNECTED" if self.is_telegram_connected() else "DISCONNECTED"
        print(f"Telegram Status: {telegram_status}")

        # Display signal processing status
        print("\nSignal Processing Status:")
        print(f"Signal Task Active: {'Yes' if hasattr(self.signal_handler, 'signal_task') and self.signal_handler.signal_task and not self.signal_handler.signal_task.done() else 'No'}")
        print(f"Signal Handler Listening: {'Yes' if hasattr(self.signal_handler, 'is_listening') and self.signal_handler.is_listening else 'No'}")
        print(f"WebSocket Connected: {'Yes' if hasattr(self, 'websocket_manager') and self.websocket_manager and self.websocket_manager.is_connected else 'No'}")

        # Display Telegram channels being monitored
        print("\nConfigured Signal Channels:")
        if hasattr(self.signal_handler, 'target_channels') and self.signal_handler.target_channels:
            print(f"Monitoring {len(self.signal_handler.target_channels)} Telegram channels:")
            for i, channel in enumerate(self.signal_handler.target_channels):  # Show all channels
                # Try to get channel name
                channel_name = "Unknown"
                try:
                    if hasattr(self.signal_handler, 'channel_names') and channel in self.signal_handler.channel_names:
                        channel_name = self.signal_handler.channel_names[channel]
                except:
                    pass
                print(f"  {i+1}. {channel} - {channel_name}")
        else:
            print("  No Telegram channels configured or loaded.")

        # Display API status
        print("\nAPI Status:")
        print(f"DexScreener API: {'ENABLED' if not self.token_analyzer.disable_dexscreener else 'DISABLED'}")
        print("Moralis API: REMOVED")
        print("PumpPortal API: REMOVED")
        print("Birdeye API: REMOVED")

        # Display API timeouts
        print("\nAPI Timeouts:")
        print(f"DexScreener: {self.token_analyzer.api_timeouts.get('dexscreener', 3.0)}s")

        # Display Helius RPC rate limiter stats
        try:
            from helius_rate_limiter import get_helius_rate_limiter
            helius_limiter = get_helius_rate_limiter()
            helius_stats = helius_limiter.get_stats()

            print("\nHelius RPC Rate Limiter:")
            print(f"  Total Requests: {helius_stats['total_requests']}")
            print(f"  Success Rate: {helius_stats['success_rate_percent']:.1f}%")
            print(f"  Rate Limited: {helius_stats['total_rate_limited']}")
            print(f"  Circuit State: {helius_stats['circuit_state'].upper()}")
            print(f"  Current RPS: {helius_stats['current_rps']}/{helius_stats['max_rps']}")
        except Exception as e:
            print(f"\nHelius RPC Rate Limiter: Error getting stats - {e}")

        # Display API stats
        print("\nAPI Stats (Last 100 calls):")
        api_stats = self.token_analyzer.get_api_stats()
        for api_name, stats in api_stats.items():
            success_rate = (stats.get('success', 0) / max(1, stats.get('total', 1))) * 100
            avg_time = stats.get('avg_time', 0) * 1000  # Convert to ms
            print(f"  {api_name}: {success_rate:.1f}% success, {avg_time:.1f}ms avg response time")

        # Get recent signals from the signal handler
        recent_signals = self.signal_handler.get_recent_signals()

        if not recent_signals:
            print("\nNo recent signals detected.")
            print("-" * 80)
        else:
            # Display recent signals with detailed information
            print("\n" + "-" * 80)
            print("RECENT SIGNALS")
            print("-" * 80)

            for i, token_address in enumerate(recent_signals[:10]):  # Show up to 10 most recent signals
                # Get detailed signal data if available
                signal_data = {}
                if hasattr(self.signal_handler, 'get_signal_data'):
                    signal_data = self.signal_handler.get_signal_data(token_address)

                # Get token confidence score
                confidence = self.signal_handler.get_token_confidence(token_address)

                # Get channel name
                channel_id = signal_data.get('source_channel', 'Unknown')
                channel_name = signal_data.get('channel', 'Unknown')

                # Get timestamp
                timestamp = signal_data.get('timestamp', 'Unknown')
                if isinstance(timestamp, datetime):
                    timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')

                # Try to get token details from analyzer
                token_name = "Unknown"
                token_symbol = "Unknown"
                token_price = 0.0
                token_liquidity = 0.0
                token_volume = 0.0
                token_mcap = 0.0

                try:
                    # Get token details from cache if available
                    token_details = self.token_analyzer.get_cached_token_details(token_address)
                    if token_details:
                        token_name = token_details.get('name', 'Unknown')
                        token_symbol = token_details.get('symbol', 'Unknown')
                        token_price = token_details.get('price', 0.0)
                        token_liquidity = token_details.get('liquidity_usd', 0.0)
                        token_volume = token_details.get('volume_24h', 0.0)
                        token_mcap = token_details.get('market_cap', 0.0)
                except Exception as e:
                    print(f"Error getting token details: {e}")

                # Display signal information
                print(f"\n[{i+1}] Token: {token_address}")
                print(f"    Name: {token_name} ({token_symbol})")
                print(f"    Price: ${token_price:.8f}")
                print(f"    Liquidity: ${token_liquidity:.2f}")
                print(f"    Volume 24h: ${token_volume:.2f}")
                print(f"    Market Cap: ${token_mcap:.2f}")
                print(f"    Confidence: {confidence:.2f}")
                print(f"    Source: {channel_name} ({channel_id})")
                print(f"    Timestamp: {timestamp}")

                # Display extracted metrics if available
                extracted_metrics = signal_data.get('extracted_metrics', {})
                if extracted_metrics:
                    print("    Extracted Metrics:")
                    for key, value in extracted_metrics.items():
                        print(f"        {key}: {value}")

                # Display message snippet
                message = signal_data.get('message', '')
                if message:
                    # Truncate message to 100 chars
                    message_snippet = message[:100] + "..." if len(message) > 100 else message
                    print(f"    Message: {message_snippet}")

                # Add separator between signals
                if i < len(recent_signals) - 1:
                    print("    " + "-" * 40)

                # Get source channel
                source_channel = signal_data.get('source', 'Unknown')
                if hasattr(self.signal_handler, 'recent_signals_info') and token_address in self.signal_handler.recent_signals_info:
                    source_channel = self.signal_handler.recent_signals_info[token_address].get('source_channel', source_channel)

                # Get confidence score
                confidence = signal_data.get('confidence', 0.0)
                if hasattr(self.signal_handler, 'get_token_confidence'):
                    confidence = self.signal_handler.get_token_confidence(token_address)

                # Get timestamp
                timestamp = signal_data.get('timestamp', None)
                timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S") if timestamp else "Unknown"

                # Truncate token address for display
                short_token = f"{token_address[:8]}...{token_address[-4:]}" if len(token_address) > 12 else token_address

                # Display signal information
                print(f"\nSignal {i+1}:")
                print(f"  Token: {short_token}")
                print(f"  Source: {source_channel}")
                print(f"  Confidence: {confidence:.2f}")
                print(f"  Timestamp: {timestamp_str}")

                # Try to get token analysis data
                try:
                    # Analyze the token to get current data using fast analysis
                    analysis = None
                    if self.fast_analysis_function and callable(self.fast_analysis_function):
                        analysis = await self.fast_analysis_function(token_address)

                    if analysis and analysis.get("exists", False):
                        # Display token information
                        token_name = analysis.get('symbol', 'UNKNOWN')
                        token_full_name = analysis.get('name', 'Unknown Token')
                        price = analysis.get('price', 0.0)
                        liquidity = analysis.get('liquidity_usd', 0.0)
                        volume = analysis.get('volume_24h', 0.0)
                        market_cap = analysis.get('market_cap', 0.0)

                        print("\n  Token Data from DexScreener:")
                        print(f"    Name: {token_name} ({token_full_name})")
                        print(f"    Price: ${price:.8f}" if price < 0.01 else f"    Price: ${price:.6f}")
                        print(f"    Liquidity: ${liquidity:,.2f}")
                        print(f"    Volume 24h: ${volume:,.2f}")
                        print(f"    Market Cap: ${market_cap:,.2f}")

                        # Display additional metrics if available
                        if 'pair_age_minutes' in analysis:
                            age_minutes = analysis['pair_age_minutes']
                            if age_minutes < 60:
                                print(f"    Age: {age_minutes} minutes")
                            else:
                                print(f"    Age: {age_minutes/60:.1f} hours")

                        if 'tx_count_24h' in analysis:
                            print(f"    24h Transactions: {analysis['tx_count_24h']:,}")

                        # Display price changes if available
                        if 'price_change_5m' in analysis:
                            print(f"    Price Change 5m: {analysis['price_change_5m']:.2f}%")
                        if 'price_change_1h' in analysis:
                            print(f"    Price Change 1h: {analysis['price_change_1h']:.2f}%")
                        if 'price_change_6h' in analysis:
                            print(f"    Price Change 6h: {analysis['price_change_6h']:.2f}%")
                        if 'price_change_24h' in analysis:
                            print(f"    Price Change 24h: {analysis['price_change_24h']:.2f}%")
                    else:
                        print("\n  Token not found in DexScreener")
                except Exception as e:
                    print(f"\n  Error analyzing token: {e}")

                # Display raw message if available
                raw_message = signal_data.get('raw_message', '')
                if raw_message and len(raw_message) > 0:
                    # Truncate message if too long
                    if len(raw_message) > 200:
                        raw_message = raw_message[:197] + "..."
                    print(f"\n  Raw Message: {raw_message}")

                print("-" * 30)

            # Show how many more signals are available
            if len(recent_signals) > 5:
                print(f"\n... and {len(recent_signals) - 5} more recent signals")

        # Display log entries related to signals
        print("\n" + "-" * 50)
        print("RECENT SIGNAL LOG ENTRIES")
        print("-" * 50)

        try:
            # Read the last 20 lines from the log file that contain "signal" or "Signal"
            import subprocess
            log_files = [f for f in os.listdir('logs') if f.startswith('bot_') and f.endswith('.log')]
            if log_files:
                # Sort by modification time (newest first)
                log_files.sort(key=lambda x: os.path.getmtime(os.path.join('logs', x)), reverse=True)
                latest_log = os.path.join('logs', log_files[0])

                # Use grep to find signal-related log entries
                try:
                    result = subprocess.run(
                        ['grep', '-i', 'signal', latest_log],
                        capture_output=True,
                        text=True
                    )
                    signal_logs = result.stdout.strip().split('\n')

                    # Display the last 10 signal-related log entries
                    if signal_logs and signal_logs[0]:
                        for log_entry in signal_logs[-10:]:
                            print(f"  {log_entry}")
                    else:
                        print("  No signal-related log entries found")
                except Exception as e:
                    print(f"  Error reading log file: {e}")
            else:
                print("  No log files found")
        except Exception as e:
            print(f"  Error accessing log files: {e}")

        print("=" * 50)

    async def check_api_status(self):
        print("Checking API Status...")
        try:
            health_status = await self.token_analyzer.check_api_health()
            print("--- API Health ---")
            for api, status in health_status.items():
                print(f"  {api}: {status}")
            print("------------------")
        except Exception as e:
            logger.error(f"Error checking API status: {e}", exc_info=True)
            print("An error occurred while checking API status.")

    async def clear_api_cache(self, minutes: int = None):
        try:
            older_than_seconds = None
            if minutes is not None:
                older_than_seconds = minutes * 60
                print(f"Clearing API Cache older than {minutes} minutes...")
            else:
                print("Clearing entire API Cache...")

            if hasattr(self.token_analyzer, 'clear_cache'):
                cleared_count = await self.token_analyzer.clear_cache(older_than_seconds=older_than_seconds)
            else:
                logger.warning("Token analyzer has no clear_cache method")
                cleared_count = 0
            print(f"Cleared {cleared_count} items from the cache.")
        except Exception as e:
             logger.error(f"Error clearing API cache: {e}", exc_info=True)
             print("An error occurred while clearing the API cache.")

    async def reset_api_stats(self):
        print("Resetting API Stats...")
        try:
            await self.token_analyzer.reset_stats()
            print("API usage statistics have been reset.")
        except Exception as e:
            logger.error(f"Error resetting API stats: {e}", exc_info=True)
            print("An error occurred while resetting API stats.")

    async def display_pump_tokens(self):
        print("Fetching and displaying Pump Tokens...")
        try:
            tokens = await self.token_analyzer.get_pump_tokens() # Assumes this method exists and returns a list of token dicts
            if not tokens:
                print("No pump tokens found or API error.")
                return

            print("\n--- Pump Tokens ---")
            for token in tokens:
                print(f"  Token: {token.get('address', 'N/A')}, Name: {token.get('name', 'N/A')}, Score: {token.get('score', 'N/A')}")
            print("-------------------")
        except NotImplementedError:
            print("Display Pump Tokens feature is not implemented in TokenAnalyzer yet.")
        except Exception as e:
            logger.error(f"Error displaying pump tokens: {e}", exc_info=True)
            print("An error occurred while fetching pump tokens.")

    async def display_moonbag_prices(self):
        print("Moonbag feature has been removed as requested.")
        return

    async def display_live_sol_price(self):
        """Display the current SOL price from API."""
        try:
            # Use our direct implementation instead of token_analyzer
            await self.get_sol_price()
        except Exception as e:
            logger.error(f"Error displaying SOL price: {e}")
            print(f"Error: {e}")

    async def display_api_stats(self):
        print("Fetching API Usage Stats...")
        try:
            stats = self.token_analyzer.get_api_stats()
            if not stats:
                print("No API usage stats available.")
                return

            print("\n--- API Usage Stats ---")
            for provider, data in stats.items():
                print(f"  Provider: {provider}")
                print(f"    Requests: {data.get('requests', 0)}")
                print(f"    Successes: {data.get('successes', 0)}")
                print(f"    Failures: {data.get('failures', 0)}")
                avg_resp_time = sum(data.get('response_times', [])) / len(data.get('response_times', [])) if data.get('response_times') else 0
                print(f"    Avg Resp Time: {avg_resp_time:.3f}s")
            print("-----------------------")
        except Exception as e:
            logger.error(f"Error displaying API stats: {e}", exc_info=True)
            print("An error occurred while fetching API stats.")

    async def view_live_stats(self):
        print("\n--- Live Stats ---")
        print(f"Status: {self.status}")
        print(f"Mode: {self.run_mode}")
        print(f"Trading Active: {self.is_trading}")
        print(f"Strategy: {self.current_strategy}")
        # Get stats from StateManager
        stats = self.state.get_stats()
        print(f"Starting SOL: {stats['starting_sol']:.4f}")
        print(f"Available SOL: {stats['current_available_sol']:.4f}")
        print(f"Open Positions: {stats['open_positions_count']}")
        print(f"Total Profit: {stats['total_profit_sol']:.4f} SOL")
        print(f"ROI: {stats['roi_percent']:.2f}%" if stats['starting_sol'] > 0 else "ROI: N/A (No starting SOL)")
        print(f"Total Trades: {stats['total_trades']}")
        print(f"Win Rate: {stats['winrate_percent']:.1f}% (W:{stats['win_count']} L:{stats['loss_count']})" if stats['total_trades'] > 0 else "Win Rate: N/A (No trades)")
        print("------------------")

    async def display_pump_fun_tokens(self):
        """Display information about Pump.fun tokens using fast analysis."""
        print("Displaying Pump.fun information...")
        try:
            # Use fast analysis for pump.fun token information
            print("Pump.fun analysis now integrated into fast token analysis pipeline")
            print("Use 'analyze [token_address]' command to analyze specific pump.fun tokens")
        except Exception as e:
            logger.error(f"Error displaying Pump.fun information: {e}", exc_info=True)
            print(f"Error: {str(e)}")
            print("Please try again later.")

    async def chat_with_ai(self):
        """
        Chat with the AI assistant through the CLI.
        This feature has been removed.
        """
        print("\n=== AI Assistant Chat ===")
        print("AI Assistant is not available in this configuration.")
        print("Returning to main menu...")
        return

    async def authorize_telegram(self):
        """Authorize Telegram connection directly with automatic recovery."""
        print("\n=== Telegram Authorization ===")
        print("This will connect to Telegram and authorize your account.")
        print("You will need to enter a verification code sent to your Telegram app.")

        # Maximum retries for connection
        max_retries = 3
        for retry in range(max_retries):
            try:
                # Try to connect to Telegram
                print(f"\nConnecting to Telegram (attempt {retry+1}/{max_retries})...")

                # Connect to Telegram
                connected = await self.signal_handler.connect()

                if connected:
                    print("✅ Successfully connected to Telegram")

                    # Try to start listening with retries
                    print("\nStarting to listen for signals...")

                    # Multiple retries for listening
                    max_listen_retries = 3
                    listening = False

                    for listen_retry in range(max_listen_retries):
                        try:
                            print(f"Starting to listen for signals (attempt {listen_retry+1}/{max_listen_retries})...")
                            listening = await self.signal_handler.start_listening()

                            if listening:
                                print(f"✅ Successfully started listening for signals on attempt {listen_retry+1}")
                                print("Telegram is now fully authorized and ready to receive signals.")

                                # Send a test message to verify everything is working
                                try:
                                    print("Sending test message to verify connection...")
                                    test_message = f"🚀 Telegram connection test successful at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                                    message_sent = await self.signal_handler.send_info_message(test_message)

                                    if message_sent:
                                        print("✅ Test message sent successfully!")
                                    else:
                                        print("⚠️ Test message could not be sent, but connection is established.")
                                except Exception as msg_error:
                                    print(f"⚠️ Error sending test message: {msg_error}")

                                return True
                            else:
                                if listen_retry < max_listen_retries - 1:
                                    print(f"⚠️ Failed to start listening on attempt {listen_retry+1}, retrying...")
                                    await asyncio.sleep(2)  # Wait before retrying
                                else:
                                    print("❌ Failed to start listening for signals after multiple attempts")
                                    print("Please check your Telegram configuration.")
                        except Exception as listen_error:
                            if listen_retry < max_listen_retries - 1:
                                print(f"⚠️ Error starting listening on attempt {listen_retry+1}: {listen_error}")
                                await asyncio.sleep(2)  # Wait before retrying
                            else:
                                print(f"❌ Error starting listening after multiple attempts: {listen_error}")
                                print("Please check your Telegram configuration.")

                    # If we get here, all listening attempts failed
                    if not listening and retry < max_retries - 1:
                        print("Retrying the entire connection process...")
                        continue
                    else:
                        return False
                else:
                    if retry < max_retries - 1:
                        print(f"⚠️ Failed to connect to Telegram on attempt {retry+1}, retrying...")
                        await asyncio.sleep(2)  # Wait before retrying
                    else:
                        print("❌ Failed to connect to Telegram after multiple attempts")
                        print("Please check your Telegram configuration.")
                        return False
            except Exception as e:
                if retry < max_retries - 1:
                    print(f"⚠️ Error during Telegram authorization attempt {retry+1}: {e}")
                    print("Retrying...")
                    await asyncio.sleep(2)  # Wait before retrying
                else:
                    logger.error(f"Error authorizing Telegram: {e}", exc_info=True)
                    print(f"❌ Error during Telegram authorization after multiple attempts: {e}")
                    print("Please check your Telegram configuration and try again.")
                    return False

        # If we get here, all attempts failed
        return False

    async def analyze_token_metrics(self, token: str):
        """Analyze token metrics with direct API calls from multiple sources"""
        # Clean the token address
        token = token.strip()
        print(f"Analyzing token metrics for {token}...")

        # Validate token address format (basic check)
        if not token or len(token) < 32 or len(token) > 44:
            print(f"Error: Invalid token address format. Length: {len(token)}. Solana addresses are typically 32-44 characters.")
            return

        # CRITICAL FIX: Check if token address ends with "pump" and ensure we use the full address
        if token.lower().endswith("pump"):
            print(f"PUMP TOKEN DETECTED: {token}")
            print(f"IMPORTANT: Using FULL address with 'pump' suffix for DexScreener API")
            # No need to modify the token address - DexScreener API handles it correctly

        try:
            # Make sure token_analyzer sessions are initialized
            if not hasattr(self.token_analyzer, 'dex_sessions_initialized') or not self.token_analyzer.dex_sessions_initialized:
                print("Initializing token analyzer sessions...")
                await self.token_analyzer.init_sessions()
                self.token_analyzer.dex_sessions_initialized = True
                print("Token analyzer sessions initialized.")

            # Use fast analysis function only - simple_pump_analyzer is the single source
            if self.fast_analysis_function and callable(self.fast_analysis_function):
                print("Using fast analysis (simple_pump_analyzer)...")
                analysis_result = await self.fast_analysis_function(token)
            else:
                print("Fast analysis not available - cannot analyze token")
                print("Error: simple_pump_analyzer is required for token analysis")
                return

            if not analysis_result:
                print("Analysis failed: No data returned from token analyzer.")
                return

            # Check if token exists
            if not analysis_result.get("exists", False):
                print(f"Token {token} not found in DexScreener.")
                print(f"Reason: {analysis_result.get('reason', 'Unknown')}")
                return

            # Format and display the metrics
            formatted_metrics = self._format_token_metrics(analysis_result)
            print(formatted_metrics)

            # Display additional information if available
            if analysis_result.get("confidence", 0) >= self.min_confidence:
                print(f"\n✅ This token meets the minimum confidence threshold for trading ({analysis_result.get('confidence', 0):.2f} >= {self.min_confidence:.2f}).")
            else:
                print(f"\n⚠️ This token's confidence score ({analysis_result.get('confidence', 0):.2f}) is below the trading threshold ({self.min_confidence:.2f}).")

            # Display token analysis data if available
            if analysis_result and analysis_result.get('exists', False):
                price = analysis_result.get('price', 0.0)
                liquidity = analysis_result.get('liquidity_usd', 0.0)
                volume = analysis_result.get('volume_24h', 0.0)
                market_cap = analysis_result.get('market_cap', 0.0)
                pair_age = analysis_result.get('pair_age_minutes', 0)

                print("\nToken Analysis:")
                print(f"Price: ${price:.8f}")
                print(f"Liquidity: ${liquidity:,.2f}")
                print(f"Volume 24h: ${volume:,.2f}")
                print(f"Market Cap: ${market_cap:,.2f}")
                print(f"Pair Age: {pair_age} minutes")

                # Display additional metrics if available
                if 'tx_count_24h' in analysis_result:
                    print(f"24h Transactions: {analysis_result['tx_count_24h']}")
                if 'holders_count' in analysis_result:
                    print(f"Holders: {analysis_result['holders_count']}")
                if 'is_pump_fun' in analysis_result and analysis_result['is_pump_fun']:
                    print("Token Type: Pump.fun token")

                # Display extracted metrics if available
                if 'extracted_metrics' in analysis_result and analysis_result['extracted_metrics']:
                    extracted = analysis_result['extracted_metrics']
                    print("\nExtracted Metrics from Signal:")
                    for key, value in extracted.items():
                        if key not in ['token_name', 'token_full_name'] and value:
                            print(f"  {key}: {value}")

        except Exception as e:
            logger.error(f"Error analyzing token metrics for {token}: {e}", exc_info=True)
            print(f"Error analyzing token: {str(e)}")
            print("Please try again later or check your network connection.")

    async def get_sol_price(self):
        """Get the current SOL price using direct API calls"""
        print("Fetching SOL/USD price...")

        try:
            # Create a new session for this request
            connector = aiohttp.TCPConnector(
                limit=10,
                ttl_dns_cache=300,
                enable_cleanup_closed=True,
                force_close=False
            )

            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as session:
                # Try DexScreener first
                sol_address = "So11111111111111111111111111111111111111112"
                dex_data = await self._fetch_dexscreener_data(session, sol_address)

                if dex_data and dex_data.get('exists', False) and dex_data.get('price', 0) > 0:
                    price = dex_data.get('price', 0)
                    print(f"Current SOL/USD Price: ${price:.2f} ")
                    return price
                else:
                    print("Could not fetch SOL price from primary source, trying fallback...")

                    # Try CoinGecko as fallback
                    try:
                        coingecko_url = "https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd"
                        async with session.get(coingecko_url, timeout=10) as response:
                            if response.status == 200:
                                data = await response.json()
                                if data and 'solana' in data and 'usd' in data['solana']:
                                    price = data['solana']['usd']
                                    print(f"Current SOL/USD Price: ${price:.2f} ")
                                    return price
                    except Exception as e:
                        logger.error(f"Error fetching SOL price from CoinGecko: {e}")

                    # If all else fails, use a hardcoded fallback price
                    print("Could not fetch SOL price from any source, using fallback price.")
                    return 153.18  # Fallback price

        except Exception as e:
            logger.error(f"Error fetching SOL price: {e}", exc_info=True)
            print(f"Error fetching SOL price: {str(e)}")
            print("Using fallback price.")
            return 153.18  # Fallback price

    async def _fetch_dexscreener_data(self, session, token_address):
        """Fetch data from DexScreener with retries"""
        # Use the endpoint format from finalconfig.json
        dexscreener_endpoint = self.config.get('api_endpoints', 'dexscreener', default="https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}")

        # CRITICAL FIX: Check if token address ends with "pump" and ensure we use the full address
        if token_address.lower().endswith("pump"):
            logger.warning(f"PUMP TOKEN DETECTED in bot_controller.py: {token_address}")
            logger.warning(f"IMPORTANT: Using FULL address with 'pump' suffix: {token_address}")
            # No need to modify the token address - DexScreener API handles it correctly

        # Replace the placeholders in the URL - NEVER modify the token address
        # First replace chainId, then tokenAddress to avoid issues with tokens containing "{chainId}"
        url = dexscreener_endpoint.replace("{chainId}", "solana").replace("{tokenAddress}", token_address)

        # Verify the final URL contains the exact token address
        if token_address not in url:
            logger.error(f"ERROR: Final URL does not contain the exact token address!")
            logger.error(f"Token address: {token_address}")
            logger.error(f"URL: {url}")
            # Try to fix the URL by directly appending the token address
            corrected_url = f"https://api.dexscreener.com/token-pairs/v1/solana/{token_address}"
            logger.warning(f"CORRECTED URL: {corrected_url}")
            url = corrected_url

        # Log the final URL with high visibility
        logger.warning(f"FINAL DexScreener API URL in bot_controller.py: {url}")

        for retry in range(3):  # Try up to 3 times
            try:
                async with session.get(url, timeout=1) as resp:  # CRITICAL FIX: 1s timeout for meme coins
                    if resp.status == 200:
                        data = await resp.json()
                        if not data or 'pairs' not in data or not data['pairs']:
                            return {"exists": False, "error": "No pairs found"}

                        # Find the most liquid pair
                        if len(data['pairs']) > 1:
                            pair = max(data['pairs'], key=lambda p: float(p.get('liquidity', {}).get('usd', 0) or 0))
                        else:
                            pair = data['pairs'][0]

                        # Extract data with safe conversion
                        try:
                            price = float(pair.get('priceUsd', 0))
                            liquidity = float(pair.get('liquidity', {}).get('usd', 0))
                            volume_24h = float(pair.get('volume', {}).get('h24', 0))
                            volume_5m = volume_24h / 288 if volume_24h else 0

                            # Calculate pair age
                            pair_age = 0
                            if pair.get('pairCreatedAt'):
                                try:
                                    from datetime import datetime, timedelta
                                    created_at = pair.get('pairCreatedAt')
                                    # Handle different date formats
                                    if isinstance(created_at, str):
                                        if 'Z' in created_at:
                                            created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                                        else:
                                            # Try parsing as timestamp
                                            created_time = datetime.fromtimestamp(int(created_at) / 1000)
                                    elif isinstance(created_at, int):
                                        # Handle timestamp in milliseconds
                                        created_time = datetime.fromtimestamp(created_at / 1000)
                                    else:
                                        # Default to current time minus 1 day if format is unknown
                                        created_time = datetime.now() - timedelta(days=1)

                                    now = datetime.now()
                                    age_minutes = (now - created_time).total_seconds() / 60
                                    pair_age = int(age_minutes)
                                except Exception as e:
                                    logger.error(f"Error calculating pair age: {e}")
                                    # Default to 60 minutes (1 hour) if calculation fails
                                    pair_age = 60

                            # Parse transaction count safely
                            try:
                                txns = pair.get('txns', {})
                                if isinstance(txns, dict) and 'h24' in txns:
                                    tx_count = int(txns['h24'])
                                else:
                                    tx_count = 0
                            except (ValueError, TypeError):
                                tx_count = 0
                            fdv = float(pair.get('fdv', 0))

                            return {
                                "exists": True,
                                "price": price,
                                "liquidity": liquidity,
                                "volume5m": volume_5m,
                                "volume24h": volume_24h,
                                "pairAge": pair_age,
                                "txCount": tx_count,
                                "fdv": fdv,
                                "marketCap": fdv  # Use FDV as market cap if not provided
                            }
                        except (ValueError, TypeError) as e:
                            logger.error(f"Error parsing DexScreener data: {e}")
                            return {"exists": False, "error": f"Data parsing error: {str(e)}"}
                    elif resp.status == 429:
                        logger.warning(f"DexScreener rate limit hit, retry {retry+1}/3")
                        await asyncio.sleep(2 ** retry)  # Exponential backoff
                        continue
                    elif 400 <= resp.status < 500 and resp.status != 429:
                        # FIX 4: Only fail permanently on 4xx client errors (except 429)
                        logger.warning(f"DexScreener client error HTTP {resp.status} - not retrying")
                        return {"exists": False, "error": f"Client error HTTP {resp.status}"}
                    else:
                        # FIX 4: Retry on 5xx server errors
                        logger.warning(f"DexScreener server error HTTP {resp.status}, retry {retry+1}/3")
                        if retry < 2:
                            await asyncio.sleep(2 ** retry)
                            continue
                        else:
                            return {"exists": False, "error": f"Server error HTTP {resp.status} after retries"}

            except Exception as e:
                logger.error(f"Error fetching DexScreener data: {e}")
                if retry < 2:  # Don't sleep on the last retry
                    await asyncio.sleep(1)

        return {"exists": False, "error": "Max retries reached"}

    async def _check_realtime_rug_signals(self, token_address: str, position_data: dict, analysis_result: dict) -> str:
        """Check for real-time rug pull signals during position monitoring."""
        try:
            # Get rug protection settings - FIXED: Use proper config access
            rug_protection_section = self.config.get_section('rug_protection') if hasattr(self.config, 'get_section') else {}
            rug_config = rug_protection_section.get('realtime_monitoring', {}) if rug_protection_section else {}
            if not rug_config.get('enable_liquidity_monitoring', True) and not rug_config.get('enable_holder_monitoring', True):
                return None

            # Get current token data from analysis result
            current_data = analysis_result.get('raw_data', {})
            if not current_data:
                return None

            # Get baseline values from position data
            baseline_liquidity = position_data.get('entry_liquidity_usd', 0)
            baseline_holders = position_data.get('entry_holder_count', 0)

            # Extract current values from DexScreener data
            dex_data = current_data.get('dexscreener', {})
            if not dex_data or 'pairs' not in dex_data or not dex_data['pairs']:
                return None

            best_pair = dex_data['pairs'][0]
            current_liquidity = float(best_pair.get('liquidity', {}).get('usd', 0) or 0)
            current_holders = int(best_pair.get('info', {}).get('holderCount', 0) or 0)

            # Check liquidity drain
            if rug_config.get('enable_liquidity_monitoring', True) and baseline_liquidity > 0:
                liquidity_drop_percent = ((baseline_liquidity - current_liquidity) / baseline_liquidity) * 100
                threshold = rug_config.get('liquidity_drain_threshold_percent', 30.0)
                if liquidity_drop_percent >= threshold:
                    return f"LIQUIDITY DRAIN: -{liquidity_drop_percent:.1f}% (${baseline_liquidity:.0f} → ${current_liquidity:.0f})"

            # Check holder exodus
            if rug_config.get('enable_holder_monitoring', True) and baseline_holders > 0:
                holder_drop_percent = ((baseline_holders - current_holders) / baseline_holders) * 100
                threshold = rug_config.get('holder_exodus_threshold_percent', 20.0)
                if holder_drop_percent >= threshold:
                    return f"HOLDER EXODUS: -{holder_drop_percent:.1f}% ({baseline_holders} → {current_holders})"

            return None

        except Exception as e:
            logger.error(f"Error checking real-time rug signals for {token_address}: {e}")
            return None

    def _format_token_metrics(self, metrics):
        """Format token metrics for display"""
        if not metrics:
            return "No metrics available"

        lines = []
        lines.append("=== Token Analysis Results ===")

        if not metrics.get('exists', False):
            lines.append(f"Analysis Failed: {metrics.get('reason', 'Unknown reason')}")
            return "\n".join(lines)

        # Show data sources
        if metrics.get('sources'):
            lines.append(f"Data Sources: {', '.join(metrics.get('sources', []))}")

        # Safe extraction with defaults
        try:
            # Basic token info
            if metrics.get('token_name'):
                lines.append(f"Name: {metrics.get('token_name', 'Unknown')}")
            if metrics.get('token_symbol'):
                lines.append(f"Symbol: {metrics.get('token_symbol', 'UNKNOWN')}")
            if metrics.get('decimals'):
                lines.append(f"Decimals: {metrics.get('decimals', 9)}")

            # Price and source
            price = metrics.get('price', 0)
            price_str = f"${price:.8f}" if price else "Unknown"
            lines.append(f"Price: {price_str}")

            source = metrics.get('price_source', 'Unknown')
            lines.append(f"Source: {source}")

            confidence = metrics.get('confidence', 0)
            lines.append(f"Confidence: {confidence:.2f}")

            # Market metrics
            market_cap = metrics.get('market_cap', 0)
            if market_cap:
                lines.append(f"Market Cap: ${market_cap:,.2f}")

            fdv = metrics.get('fdv', 0)
            if fdv:
                lines.append(f"Fully Diluted Value: ${fdv:,.2f}")

            # Liquidity with source and multiplier info
            liquidity_usd = metrics.get('liquidity_usd', 0)
            liquidity_source = metrics.get('liquidity_source', 'Unknown')
            show_multiplier = metrics.get('show_multiplier', False)

            liquidity_display = f"${liquidity_usd:,.2f}"
            if show_multiplier:
                liquidity_display += " (x1.95 calculated)"
            lines.append(f"Liquidity ({liquidity_source}): {liquidity_display}")

            liquidity_sol = metrics.get('liquidity_sol', 0)
            if liquidity_sol:
                lines.append(f"Liquidity (SOL): {liquidity_sol:,.2f} SOL")

            # Volume
            volume_5m = metrics.get('volume_5m', 0)
            if volume_5m:
                lines.append(f"5m Volume: ${volume_5m:,.2f}")

            volume24h = metrics.get('volume24h', 0)
            if volume24h:
                lines.append(f"24h Volume: ${volume24h:,.2f}")

            # Age and transactions
            pair_age = metrics.get('pair_age_minutes', 0)
            if pair_age:
                lines.append(f"Pair Age: {pair_age} minutes")

            tx_count = metrics.get('tx_count_24h', 0)
            if tx_count:
                lines.append(f"24h Transactions: {tx_count}")

            # Moralis-specific data
            holders_count = metrics.get('holders_count', 0)
            if holders_count:
                lines.append(f"Holders Count: {holders_count}")

            top_holder_percent = metrics.get('top_holder_percent', 0)
            if top_holder_percent:
                lines.append(f"Top Holder %: {top_holder_percent:.2f}%")

            dev_wallets_percent = metrics.get('dev_wallets_percent', 0)
            if dev_wallets_percent:
                lines.append(f"Dev Wallets %: {dev_wallets_percent:.2f}%")

            # Safety flags
            if metrics.get('safety_flags'):
                lines.append(f"Safety Flags: {', '.join(metrics.get('safety_flags', []))}")

            # Token properties
            if 'is_freezable' in metrics:
                lines.append(f"Freezable: {'Yes' if metrics.get('is_freezable') else 'No'}")

            # Pump.fun specific information (simplified)
            if metrics.get('is_pump_fun', False):
                lines.append("\n=== Pump.fun Token Information ===")
                lines.append("This token appears to be from Pump.fun (address ends with 'pump')")
                lines.append("Note: Detailed Pump.fun data is no longer available")
                lines.append("=== End Pump.fun Information ===")

            # Display extracted metrics from signal message if available
            if metrics.get('extracted_metrics'):
                extracted = metrics.get('extracted_metrics')
                lines.append("\n=== Extracted Metrics from Signal ===")

                if extracted.get('token_name'):
                    lines.append(f"Token Name: ${extracted.get('token_name')} ({extracted.get('token_full_name', '')})")
                if extracted.get('volume'):
                    lines.append(f"Volume: ${extracted.get('volume')}")
                if extracted.get('liquidity_usd'):
                    lines.append(f"Liquidity: ${extracted.get('liquidity_usd')}")
                if extracted.get('mcp'):
                    lines.append(f"Market Cap: ${extracted.get('mcp')}")
                if extracted.get('holders'):
                    lines.append(f"Holders: {extracted.get('holders')}")
                if extracted.get('open'):
                    lines.append(f"Age: {extracted.get('open')}")
                if extracted.get('txs'):
                    lines.append(f"Transactions: {extracted.get('txs')}")
                if extracted.get('price_change_5m'):
                    lines.append(f"Price Change (5m): {extracted.get('price_change_5m')}%")
                if extracted.get('price_change_1h'):
                    lines.append(f"Price Change (1h): {extracted.get('price_change_1h')}%")
                if extracted.get('price_change_6h'):
                    lines.append(f"Price Change (6h): {extracted.get('price_change_6h')}%")

                lines.append("=== End Extracted Metrics ===")

            # Sentiment
            sentiment = metrics.get('sentiment', 0.5)
            lines.append(f"Sentiment: {sentiment:.2f}")

            # Reason for confidence
            if metrics.get('reason'):
                lines.append(f"Analysis: {metrics.get('reason')}")

            # Show analysis time if available (from simple_pump_analyzer)
            if metrics.get('analysis_time'):
                lines.append(f"Analysis Time: {metrics.get('analysis_time'):.2f}s")

            # Show all available data from simple_pump_analyzer for debugging
            if metrics.get('raw_data'):
                raw_data = metrics.get('raw_data', {})
                if 'dexscreener' in raw_data:
                    dex_data = raw_data['dexscreener']
                    lines.append("\n=== Simple Pump Analyzer Data ===")
                    lines.append(f"DEX ID: {dex_data.get('dex_id', 'Unknown')}")
                    lines.append(f"Pair Address: {dex_data.get('pair_address', 'Unknown')}")
                    # Calculate price native from USD price and SOL price
                    price_usd = metrics.get('price', 0)
                    sol_price = getattr(self.token_analyzer, 'sol_usd_price', 151.37) or 151.37
                    price_native = price_usd / sol_price if sol_price > 0 else 0
                    lines.append(f"Price Native: {price_native:.8f} SOL")
                    lines.append(f"DexScreener Liquidity: ${dex_data.get('liquidity_usd', 0):,.2f}")

                    # Show pool SOL balance and calculation details
                    pool_sol = metrics.get('pool_sol', 0)
                    if pool_sol > 0:
                        lines.append(f"Pool SOL Balance: {pool_sol:.6f} SOL")
                        # Get SOL price from token analyzer or use fallback
                        sol_price = getattr(self.token_analyzer, 'sol_usd_price', 151.37) or 151.37
                        calculated_liq = pool_sol * sol_price * 1.95
                        lines.append(f"SOL Price: ${sol_price:.2f}")
                        lines.append(f"Calculated Liquidity: {pool_sol:.6f} × ${sol_price:.2f} × 1.95 = ${calculated_liq:,.2f}")
                    else:
                        lines.append(f"Pool SOL Balance: {pool_sol} SOL (not found)")

                    lines.append(f"Volume 5m: ${dex_data.get('volume_m5', 0):,.2f}")
                    lines.append(f"Volume 1h: ${dex_data.get('volume_h1', 0):,.2f}")
                    lines.append(f"Volume 6h: ${dex_data.get('volume_h6', 0):,.2f}")
                    lines.append(f"Volume 24h: ${dex_data.get('volume_h24', 0):,.2f}")
                    lines.append(f"Price Change 5m: {dex_data.get('price_change_m5', 0):+.1f}%")
                    lines.append(f"Price Change 1h: {dex_data.get('price_change_h1', 0):+.1f}%")
                    lines.append(f"Price Change 6h: {dex_data.get('price_change_h6', 0):+.1f}%")
                    lines.append(f"Price Change 24h: {dex_data.get('price_change_h24', 0):+.1f}%")
                    # Enhanced transaction data with percentages
                    lines.append("Transactions:")

                    # 5m transactions
                    buys_5m = metrics.get('txns_5m_buys', 0)
                    sells_5m = metrics.get('txns_5m_sells', 0)
                    total_5m = buys_5m + sells_5m
                    buy_pct_5m = (buys_5m / total_5m * 100) if total_5m > 0 else 0
                    lines.append(f"   5m: {buys_5m} buys, {sells_5m} sells ({buy_pct_5m:.0f}% buys)")

                    # 1h transactions
                    buys_1h = metrics.get('txns_1h_buys', 0)
                    sells_1h = metrics.get('txns_1h_sells', 0)
                    total_1h = buys_1h + sells_1h
                    buy_pct_1h = (buys_1h / total_1h * 100) if total_1h > 0 else 0
                    lines.append(f"   1h: {buys_1h} buys, {sells_1h} sells ({buy_pct_1h:.0f}% buys)")

                    # 6h transactions
                    buys_6h = metrics.get('txns_6h_buys', 0)
                    sells_6h = metrics.get('txns_6h_sells', 0)
                    total_6h = buys_6h + sells_6h
                    buy_pct_6h = (buys_6h / total_6h * 100) if total_6h > 0 else 0
                    lines.append(f"   6h: {buys_6h} buys, {sells_6h} sells ({buy_pct_6h:.0f}% buys)")

                    # 24h transactions
                    buys_24h = metrics.get('txns_24h_buys', 0)
                    sells_24h = metrics.get('txns_24h_sells', 0)
                    total_24h = buys_24h + sells_24h
                    buy_pct_24h = (buys_24h / total_24h * 100) if total_24h > 0 else 0
                    lines.append(f"   24h: {buys_24h} buys, {sells_24h} sells ({buy_pct_24h:.0f}% buys)")

                    # Detailed holder information
                    holders = metrics.get('holder_count', 0)
                    top_10_pct = metrics.get('top_10_percentage', 0)
                    if holders > 0:
                        lines.append(f"👥 Total holders: {holders}")
                    if top_10_pct > 0:
                        lines.append(f"🔝 Top 10 holders: {top_10_pct:.2f}%")

                    # Token age information
                    token_age_minutes = metrics.get('token_age_minutes', 0)
                    creation_date = metrics.get('creation_date', 'Unknown')
                    if token_age_minutes > 0:
                        if token_age_minutes < 60:
                            age_str = f"{int(token_age_minutes)} minutes old"
                        elif token_age_minutes < 1440:  # Less than 24 hours
                            hours = token_age_minutes / 60
                            age_str = f"{hours:.1f} hours old"
                        else:
                            days = token_age_minutes / 1440
                            age_str = f"{days:.1f} days old"
                        lines.append(f"🎂 Token Age: {age_str}")
                        lines.append(f"📅 Created: {creation_date}")

                    if dex_data.get('pair_created_at'):
                        lines.append(f"Pair Created: {dex_data.get('pair_created_at')}")
                    lines.append("=== End Simple Pump Analyzer Data ===")

        except Exception as e:
            lines.append(f"Error formatting metrics: {str(e)}")

        lines.append("===========================")
        return "\n".join(lines)

    # --- Added Placeholder Methods for New CLI Options ---
    async def send_test_message(self, message: str) -> bool:
        """Sends a test message to the configured info channel with enhanced debugging and wallet status."""
        try:
            # Check if client is connected first
            if not self.signal_handler.client:
                print("Error: Telegram client not initialized. Please authorize Telegram first (option 12).")
                return False

            # Skip connection check as it's causing issues

            # Get the info channel entity
            try:
                info_channel_id_str = self.signal_handler.telegram_settings.get('bot_info_channel_id')
                if not info_channel_id_str:
                    print("Error: 'bot_info_channel_id' not set in config.")
                    return False

                # Ensure the ID is an integer for get_entity
                # Strip any quotes that might be in the string
                info_channel_id_str = str(info_channel_id_str).strip('"\'')
                info_channel_id = int(info_channel_id_str)

                print(f"Testing Telegram connection to channel ID: {info_channel_id}")

                # Try multiple approaches to ensure message delivery
                success = False

                # Get wallet balance and PnL information
                balance = self.state.available_sol
                # FIXED: Use consistent counters for winrate calculation
                total_trades = self.state.win_count + self.state.loss_count
                win_rate = (self.state.win_count / total_trades * 100) if total_trades > 0 else 0
                total_pnl = self.state.total_profit_sol

                # Get SOL/USD price for USD value calculation
                sol_usd_price = self.token_analyzer.sol_usd_price or 150.0  # Fallback price
                balance_usd = balance * sol_usd_price
                total_pnl_usd = total_pnl * sol_usd_price

                # Get open positions count
                open_positions = len(self.state.get_open_positions())

                # Create enhanced test message with wallet status
                enhanced_message = (
                    f"🚀 <b>Bot Connection Test</b>\n\n"
                    f"Message: {message}\n"
                    f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                    f"<b>Wallet Status:</b>\n"
                    f"Balance: {balance:.4f} SOL (${balance_usd:.2f})\n"
                    f"Open Positions: {open_positions}\n"
                    f"Total Trades: {total_trades}\n"
                    f"Winrate: {win_rate:.1f}%\n"
                    f"Total PnL: {total_pnl:.4f} SOL (${total_pnl_usd:.2f})\n\n"
                    f"<b>Bot Status:</b>\n"
                    f"Mode: {self.run_mode}\n"
                    f"Strategy: {self.current_strategy}\n"
                    f"Status: {self.status}"
                )

                # Approach 1: Try using the send_info_message method
                print("Approach 1: Using send_info_message method...")
                try:
                    if await self.signal_handler.send_info_message(enhanced_message):
                        print("✅ Test message sent successfully using send_info_message")
                        success = True
                    else:
                        print("❌ send_info_message returned False")
                except Exception as e:
                    print(f"Error using send_info_message: {e}")

                # Approach 2: Try direct message to channel ID if first approach failed
                if not success:
                    print("Approach 2: Sending direct message to channel ID...")
                    try:
                        await self.signal_handler.client.send_message(
                            info_channel_id,
                            enhanced_message,
                            parse_mode='html'
                        )
                        print(f"✅ Test message sent successfully to channel {info_channel_id}")
                        success = True
                    except Exception as e:
                        print(f"Error sending direct message: {e}")

                # Approach 3: Try to resolve the entity first if previous approaches failed
                if not success:
                    print("Approach 3: Resolving entity first...")
                    try:
                        entity = await self.signal_handler.client.get_entity(info_channel_id)

                        # Log entity details
                        entity_type = type(entity).__name__
                        entity_id = getattr(entity, 'id', 'unknown')
                        entity_title = getattr(entity, 'title', 'unknown')
                        print(f"Resolved entity: Type={entity_type}, ID={entity_id}, Title={entity_title}")

                        await self.signal_handler.client.send_message(
                            entity,
                            enhanced_message,
                            parse_mode='html'
                        )
                        print(f"✅ Test message sent successfully to entity {entity.id}")
                        success = True
                    except Exception as e2:
                        print(f"Error sending message to resolved entity: {e2}")

                return success

            except ValueError:
                print(f"Error: Invalid bot_info_channel_id in config: '{info_channel_id_str}'. Must be an integer.")
                return False
            except Exception as e:
                print(f"Error sending message: {e}")
                import traceback
                print(traceback.format_exc())
                return False
        except Exception as e:
            print(f"Error sending test message: {e}")
            import traceback
            print(traceback.format_exc())
            return False

    async def view_bot_status(self):
        """Displays the overall status of the bot."""
        print("\n--- Bot Status ---")
        print(f"Trading Status: {self.status}")
        print(f"Trading Mode: {self.run_mode}")
        print(f"Trading Active: {self.is_trading}")
        # Add more status info as needed (e.g., component health)
        print("------------------")

    async def view_signal_status(self):
        """Displays the status of signal processing with detailed information similar to telegram_signal_monitor.
        This is the dedicated signal debugging option that shows detailed signal information without flooding the CLI.
        All signal debugging output is now moved under this option to keep the main console clean.
        """
        print("\n" + "=" * 80)
        print("SIGNAL STATUS AND MONITORING INFORMATION (OPTION 33 - SIGNAL DEBUG)")
        print("=" * 80)

        # Temporarily enable debug logging for signal handler
        signal_logger = logging.getLogger('signal_handler')
        original_level = signal_logger.level
        signal_logger.setLevel(logging.INFO)

        # We'll restore the original level at the end of this method

        # Check if simulated signals are enabled
        simulated_signals_enabled = not hasattr(self.signal_handler, 'disable_simulated_signals') or not self.signal_handler.disable_simulated_signals

        # Get signal queue size
        signal_queue_size = self.signal_handler.signal_queue.qsize()

        # Get recent signals (get more signals to show)
        recent_signals = self.signal_handler.recent_signals[-15:] if hasattr(self.signal_handler, 'recent_signals') else []
        # Reverse to show newest first
        recent_signals = list(reversed(recent_signals))

        # Get wallet balance and PnL information
        balance = self.state.available_sol
        # FIXED: Use consistent counters for winrate calculation
        total_trades = self.state.win_count + self.state.loss_count
        win_rate = (self.state.win_count / total_trades * 100) if total_trades > 0 else 0
        total_pnl = self.state.total_profit_sol

        # Get SOL/USD price for USD value calculation
        sol_usd_price = self.token_analyzer.sol_usd_price or 150.0  # Fallback price
        balance_usd = balance * sol_usd_price

        # Display summary information
        print(f"Signal Queue Size: {signal_queue_size}")
        print(f"Simulated Signals: {'ENABLED' if simulated_signals_enabled else 'DISABLED'}")
        print(f"Trading Mode: {self.run_mode}")
        print(f"Trading Status: {self.status}")
        print(f"Strategy: {self.current_strategy}")

        # Check if telegram_status attribute exists
        telegram_status = "DISCONNECTED"
        if hasattr(self.signal_handler, 'telegram_status'):
            telegram_status = self.signal_handler.telegram_status
        elif hasattr(self.signal_handler, 'client') and self.signal_handler.client:
            telegram_status = "CONNECTED"

        print(f"Telegram Status: {telegram_status}")

        # Display wallet information
        print("\nWallet Status:")
        print(f"Balance: {balance:.4f} SOL (${balance_usd:.2f})")
        print(f"Total Trades: {total_trades}")
        print(f"Winrate: {win_rate:.1f}%")
        print(f"Total PnL: {total_pnl:.4f} SOL")

        # Display signal processing status
        print("\nSignal Processing Status:")
        print(f"Signal Task Active: {'Yes' if self._signal_task and not self._signal_task.done() else 'No'}")
        print(f"Signal Handler Listening: {'Yes' if hasattr(self.signal_handler, 'is_listening') and self.signal_handler.is_listening else 'No'}")
        print(f"WebSocket Connected: {'Yes' if hasattr(self.websocket_manager, 'connected') and getattr(self.websocket_manager, 'connected', False) else 'No'}")

        # Display channel information
        print("\nMonitored Telegram Channels:")
        target_channels = self.signal_handler.telegram_settings.get('target_channels', [])
        if target_channels:
            for i, channel_id in enumerate(target_channels, 1):
                # Try to get channel name if available
                channel_name = "Unknown"
                if hasattr(self.signal_handler, 'channel_names') and channel_id in self.signal_handler.channel_names:
                    channel_name = self.signal_handler.channel_names.get(channel_id)
                print(f"{i}. {channel_name} (ID: {channel_id})")
        else:
            print("No Telegram channels configured.")

        # Display detailed signal handler debug information
        print("\n" + "=" * 80)
        print("SIGNAL HANDLER DEBUG INFORMATION")
        print("=" * 80)

        # Display cooldown information
        print("\nCooldown Settings:")
        print(f"Token Cooldown Period: {self.signal_handler.cooldown_period} seconds")
        print(f"Channel Cooldown Period: {self.signal_handler.channel_cooldown_period} seconds")

        # Display active cooldowns
        print("\nActive Token Cooldowns:")
        if hasattr(self.signal_handler, 'cooldowns') and self.signal_handler.cooldowns:
            for token, timestamp in self.signal_handler.cooldowns.items():
                time_remaining = max(0, timestamp + self.signal_handler.cooldown_period - time.time())
                print(f"  {token}: {time_remaining:.1f} seconds remaining")
        else:
            print("  No active token cooldowns")

        print("\nActive Channel Cooldowns:")
        if hasattr(self.signal_handler, 'channel_cooldowns') and self.signal_handler.channel_cooldowns:
            for channel, timestamp in self.signal_handler.channel_cooldowns.items():
                time_remaining = max(0, timestamp + self.signal_handler.channel_cooldown_period - time.time())
                print(f"  {channel}: {time_remaining:.1f} seconds remaining")
        else:
            print("  No active channel cooldowns")

        # Display recent sell notifications
        print("\n" + "=" * 50)
        print("RECENT SELL NOTIFICATIONS")
        print("=" * 50)

        # First check for sell notifications in the signal handler's message queue
        sell_notifications = []
        if hasattr(self.signal_handler, 'get_recent_sell_notifications'):
            sell_notifications = self.signal_handler.get_recent_sell_notifications(5)

        if sell_notifications:
            print("\nPending Sell Notifications in Queue:")
            for i, notification in enumerate(sell_notifications, 1):
                message = notification.get('message', 'Unknown')
                message_id = notification.get('id', 'Unknown')
                # Truncate message to 100 chars
                message_snippet = message[:100] + "..." if len(message) > 100 else message
                print(f"{i}. [{message_id}] {message_snippet}")
            print("\n" + "-" * 40)

        # Also check for recent sell events in trade history
        recent_sells = []
        if hasattr(self.state, 'trade_history'):
            # Get the last 5 sell events from trade history
            for trade in reversed(self.state.trade_history[-20:]):
                if trade.get('event_type') in ['sell', 'partial_sell']:
                    recent_sells.append(trade)
                    if len(recent_sells) >= 5:
                        break

        if recent_sells:
            for i, sell in enumerate(recent_sells, 1):
                token = sell.get('token', 'Unknown')
                event_type = sell.get('event_type', 'Unknown')
                entry_price = sell.get('entry_price', 0)
                exit_price = sell.get('exit_price', 0)
                pnl_sol = sell.get('pnl_sol', 0)
                reason = sell.get('reason', 'Unknown')
                timestamp = sell.get('timestamp', 'Unknown')

                # Calculate profit percentage
                if entry_price > 0:
                    profit_percent = ((exit_price / entry_price) - 1) * 100
                else:
                    profit_percent = 0

                # Display sell notification
                print(f"\nSELL #{i} | {timestamp}")
                print(f"Token: {token}")
                print(f"Type: {event_type.upper()}")
                print(f"Entry: ${entry_price:.8f} | Exit: ${exit_price:.8f}")
                print(f"PnL: {pnl_sol:.4f} SOL ({profit_percent:.2f}%)")
                print(f"Reason: {reason}")
        else:
            print("\nNo recent sell notifications found in trade history.")

        # Display recent signals with detailed information
        if recent_signals:
            print("\n" + "=" * 50)
            print(f"RECENT SIGNALS (Last {len(recent_signals)})")
            print("=" * 50)

            for i, token_address in enumerate(recent_signals, 1):
                # Get confidence score from token_confidence dictionary
                confidence = self.signal_handler.token_confidence.get(token_address, 0.0)

                # Get detailed signal data if available
                signal_data = {}
                if hasattr(self.signal_handler, 'recent_signal_data'):
                    signal_data = self.signal_handler.recent_signal_data.get(token_address, {})

                source = signal_data.get('source', 'Unknown')
                timestamp = signal_data.get('timestamp', datetime.now())
                if isinstance(timestamp, datetime):
                    timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    timestamp_str = str(timestamp)

                # Format token address for display (not used in this implementation)

                # Get analysis result if available
                analysis_data = {}
                if hasattr(self, 'token_analyzer'):
                    # Check if we have cached analysis data
                    if hasattr(self.token_analyzer, 'analysis_cache') and token_address in self.token_analyzer.analysis_cache:
                        analysis_data = self.token_analyzer.analysis_cache.get(token_address, {})

                # Display signal with detailed information
                confidence_emoji = "✅" if confidence >= 0.7 else "⚠️" if confidence >= 0.4 else "❌"
                print(f"\nSIGNAL #{i} | {timestamp_str}")
                print(f"Token: {token_address}")
                print(f"Source: {source}")
                print(f"Confidence: {confidence_emoji} {confidence:.2f}")

                # Display token analysis data if available
                if analysis_data and analysis_data.get('exists', False):
                    price = analysis_data.get('price', 0.0)
                    liquidity = analysis_data.get('liquidity_usd', 0.0)
                    volume = analysis_data.get('volume_24h', 0.0)
                    market_cap = analysis_data.get('market_cap', 0.0)
                    pair_age = analysis_data.get('pair_age_minutes', 0)

                    print("\nToken Analysis:")
                    print(f"Price: ${price:.8f}")
                    print(f"Liquidity: ${liquidity:,.2f}")
                    print(f"Volume 24h: ${volume:,.2f}")
                    print(f"Market Cap: ${market_cap:,.2f}")
                    print(f"Pair Age: {pair_age} minutes")

                    # Display token name and symbol if available
                    if analysis_data.get('token_name'):
                        print(f"Name: {analysis_data.get('token_name')}")
                    if analysis_data.get('token_symbol'):
                        print(f"Symbol: {analysis_data.get('token_symbol')}")

                    # Display reason for confidence score if available
                    if analysis_data.get('reason'):
                        print(f"Analysis: {analysis_data.get('reason')}")

                # Display links
                print("\nLinks:")
                print(f"DexScreener: https://dexscreener.com/solana/{token_address}")
                print(f"Birdeye: https://birdeye.so/token/{token_address}")
                print(f"Solscan: https://solscan.io/token/{token_address}")
                print(f"Raydium: https://raydium.io/swap/?inputCurrency={token_address}")
                print(f"Pump.fun: https://pump.fun/token/{token_address}")

                print("-" * 50)
        else:
            print("\nNo recent signals detected.")

        # Try to get the last error if any
        if hasattr(self.signal_handler, 'last_error'):
            last_error = self.signal_handler.last_error
            if last_error:
                print("\nLast Error:")
                print(f"{last_error}")

        print("=" * 50)

        # Restore original logging level
        signal_logger.setLevel(original_level)
        print(f"\nSignal handler logging level restored to {logging.getLevelName(original_level)}")

    async def authorize_telegram(self):
        """Simple Telegram authorization process with rate limiting."""
        print("Checking Telegram connection...")

        # Check if we've already attempted authorization recently
        current_time = time.time()
        last_auth_attempt = getattr(self, '_last_auth_attempt', 0)

        # Check if the session file exists
        session_file = f"{self.signal_handler.session_path}.session"
        session_exists = os.path.exists(session_file)

        # Limit to one authorization attempt per 5 minutes, but bypass if session file doesn't exist
        # or if _last_auth_attempt was reset to 0 by the _fix_corrupted_state method
        if current_time - last_auth_attempt < 300 and session_exists and last_auth_attempt > 0:  # 5 minutes in seconds
            print("⚠️ Please wait before attempting another Telegram authorization.")
            print(f"⚠️ You can try again in {int(300 - (current_time - last_auth_attempt))} seconds.")
            return

        # Update last attempt timestamp
        self._last_auth_attempt = current_time

        try:
            # If we don't have a client yet, create one
            if not self.signal_handler.client:
                print("Creating new Telegram client...")
                await self.signal_handler.connect()

                if not self.signal_handler.client:
                    print("Failed to create Telegram client. Check your API credentials.")
                    return

                print("Telegram client created.")

            # If client exists but not connected, connect it
            if not self.signal_handler.client.is_connected():
                print("Connecting to Telegram...")
                try:
                    await self.signal_handler.client.connect()
                    print("Connected to Telegram.")
                except Exception as e:
                    print(f"Error connecting to Telegram: {e}")
                    return

            # Check authorization status
            try:
                is_authorized = await self.signal_handler.client.is_user_authorized()

                if is_authorized:
                    print("✅ Telegram client is authorized. You can now use Telegram functionality.")
                else:
                    print("\n" + "=" * 60)
                    print("❌ TELEGRAM AUTHORIZATION REQUIRED")
                    print("=" * 60)

                    print("\n⚠️ IMPORTANT: Too many authorization attempts can lead to a temporary ban.")
                    print("⚠️ Make sure you have your phone nearby before proceeding.")

                    print("\nThe authorization process has two steps:")
                    print("1. Enter your phone number (or use the one from .env)")
                    print("2. Enter the verification code sent to your Telegram app")

                    print("\nTo proceed with authorization:")
                    print("1. Continue with authorization (you'll need to enter a verification code)")
                    print("2. Return to menu (if you want to try again later)")

                    choice = input("\nEnter your choice (1 or 2): ")

                    if choice != "1":
                        print("Returning to menu. You can try again later.")
                        return

                    # Get phone number from .env file
                    env_phone = self.config.get_telegram_phone()
                    if env_phone and env_phone.strip():
                        print(f"\nUsing phone number from .env file: {env_phone}")
                        phone = env_phone

                        # Automatically proceed with the phone number from .env
                        print("\nAutomatically proceeding with phone number from .env file.")
                        print("This will send a verification code to your phone.")
                    else:
                        # Fallback to config file
                        config_phone = self.config.get('telegram_settings', 'phone_number')
                        if config_phone:
                            print(f"\nUsing phone number from config: {config_phone}")
                            phone = config_phone
                            print("\nAutomatically proceeding with phone number from config.")
                            print("This will send a verification code to your phone.")
                        else:
                            phone = input("\nPhone number (with country code, e.g. +**********): ")

                    # Validate phone number format
                    if not phone.startswith("+") or not phone[1:].isdigit():
                        print("❌ Invalid phone number format. Please use format +**********")
                        return

                    # Send code request - ONLY ONE REQUEST
                    try:
                        print("\nSending verification code request...")
                        try:
                            await self.signal_handler.client.send_code_request(phone)
                            print("\n" + "=" * 60)
                            print("✅ VERIFICATION CODE SENT TO YOUR PHONE")
                            print("=" * 60)
                            print("\n📱 Check your Telegram app on your phone for the verification code")
                            print("📝 The code is usually 5 digits and will be sent by Telegram")
                            print("⏱️ The code is valid for a limited time\n")
                        except Exception as code_request_error:
                            error_msg = str(code_request_error).lower()
                            print(f"❌ Error sending code request: {code_request_error}")

                            # Check if this is a rate limiting error
                            if "already used" in error_msg or "resend" in error_msg:
                                print("\n" + "=" * 60)
                                print("⚠️ TELEGRAM RATE LIMIT REACHED")
                                print("=" * 60)
                                print("\n📱 If you already received a verification code on your phone,")
                                print("📝 you can still enter it below to complete the authorization.")
                                print("⏱️ If you don't have a code, please wait 24 hours before trying again.\n")
                            else:
                                # For other errors, return to menu
                                print("Please try again later (option 12) after waiting at least 24 hours.")
                                return

                        # Always allow entering the code, even if sending a new one failed
                        code = input("👉 ENTER THE VERIFICATION CODE HERE: ")

                        # Try to sign in - ONLY ONE ATTEMPT
                        try:
                            print("\nAttempting to sign in...")
                            await self.signal_handler.client.sign_in(phone, code)
                            print("✅ Successfully authorized!")
                        except Exception as e:
                            error_msg = str(e).lower()
                            if "password" in error_msg:
                                # 2FA is enabled
                                print("\n" + "=" * 60)
                                print("TWO-FACTOR AUTHENTICATION REQUIRED")
                                print("=" * 60)
                                print("\n🔐 Your Telegram account has 2FA enabled")
                                print("🔑 You need to enter your Telegram 2FA password (not the verification code)")
                                password = input("\n👉 ENTER YOUR 2FA PASSWORD: ")
                                try:
                                    await self.signal_handler.client.sign_in(password=password)
                                    print("✅ Successfully authorized with 2FA!")
                                except Exception as e2:
                                    print(f"❌ Error during 2FA sign-in: {e2}")
                                    print("Please try again later (option 12) after waiting at least 5 minutes.")
                            else:
                                print(f"❌ Error during sign-in: {e}")
                                print("Please try again later (option 12) after waiting at least 5 minutes.")
                    except Exception as e:
                        error_msg = str(e).lower()
                        print(f"❌ Error during Telegram authorization: {e}")

                        if "banned" in error_msg or "flood" in error_msg:
                            print("\n⚠️ Your account may have been temporarily banned by Telegram.")
                            print("⚠️ This can happen if you make too many authorization attempts.")
                            print("⚠️ Please wait 24 hours before trying again.")
                        elif "invalid" in error_msg and "phone" in error_msg:
                            print("\n⚠️ The phone number format is invalid. Make sure to include the country code.")
                            print("⚠️ Example: +**********")

            except Exception as e:
                print(f"Error checking authorization status: {e}")

        except Exception as e:
            print(f"Error during Telegram setup: {e}")

        # No need to update status display - it will be updated automatically

    async def check_websocket_status(self):
        """Checks and displays the status of the WebSocket connection."""
        print("Checking WebSocket Connection Status...")
        # Implementation: status = await self.websocket_manager.check_status()
        # print(f"WebSocket Status: {status}")
        status = await self.websocket_manager.check_status() # Assumes method exists
        print(f"WebSocket Connection Status: {status}")

    async def fix_corrupted_state(self):
        """Fix corrupted state files"""
        return self.state.fix_corrupted_state()

    async def fix_telegram_session(self):
        """Fix corrupted Telegram session"""
        import os
        import shutil
        from datetime import datetime

        print("\n=== Fixing Telegram Session ===")

        # Create logs directory
        os.makedirs("telegram_logs", exist_ok=True)

        # Create backup directory if it doesn't exist
        backup_dir = os.path.join("telegram_logs", "backups")
        os.makedirs(backup_dir, exist_ok=True)

        # Get session path from signal handler
        session_path = self.signal_handler.session_path
        session_file = f"{session_path}.session"

        # Check if session file exists
        if os.path.exists(session_file):
            try:
                # Generate backup filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = os.path.join(backup_dir, f"session_backup_{timestamp}.session")

                # Create backup
                shutil.copy2(session_file, backup_path)
                print(f"✅ Created backup at {backup_path}")

                # Disconnect client if it exists
                if self.signal_handler.client:
                    try:
                        print("Disconnecting Telegram client...")
                        await self.signal_handler.client.disconnect()
                        print("✅ Telegram client disconnected")
                    except Exception as e:
                        print(f"⚠️ Warning: Error disconnecting client: {e}")

                # Set client to None
                self.signal_handler.client = None

                # Delete the session file
                os.remove(session_file)
                print(f"✅ Deleted session file: {session_file}")
                print("\n✅ Telegram session has been reset successfully.")
                print("✅ Please use option 12 to authorize again.")
                return True
            except Exception as e:
                print(f"❌ Error fixing Telegram session: {e}")
                return False
        else:
            # If session file doesn't exist, check for other Telegram-related files
            print(f"Session file not found: {session_file}")

            # Check for other session files
            session_files = []
            for file in os.listdir():
                if file.endswith(".session"):
                    session_files.append(file)

            if session_files:
                print(f"\nFound {len(session_files)} other session files:")
                for i, file in enumerate(session_files, 1):
                    print(f"{i}. {file}")

                choice = input("\nWould you like to delete these files? (yes/no): ")
                if choice.lower() == "yes":
                    for file in session_files:
                        try:
                            os.remove(file)
                            print(f"✅ Deleted: {file}")
                        except Exception as e:
                            print(f"❌ Error deleting {file}: {e}")
                    print("\n✅ All session files deleted.")
                    return True
            else:
                print("\n✅ No Telegram session files found. Nothing to fix.")

            # Reset the client anyway
            self.signal_handler.client = None
            print("\n✅ Telegram client has been reset.")
            print("✅ Please use option 12 to authorize again.")
            return True

    async def get_balance(self) -> float:
        """Get the current SOL balance with intelligent routing (real trading only)."""
        # Real trading only - always use actual wallet balance
        # ENTERPRISE FIX: Intelligent routing with graceful fallback
        # Try unified trader first, fallback to legacy PumpPortal for compatibility
        try:
            if hasattr(self, 'unified_trader') and self.unified_trader and self._unified_trader_initialized:
                logger.debug("Using unified trader for balance check")
                balance = await self.unified_trader.get_balance()
                if balance is not None and balance >= 0:
                    return balance
                else:
                    logger.warning("Unified trader returned invalid balance, falling back to PumpPortal")

            # Graceful fallback to legacy system (preserves existing functionality)
            logger.debug("Using PumpPortal trader for balance check (legacy mode)")
            balance = await self.pumpportal_trader.get_balance()
            if balance is not None:
                return balance
            else:
                logger.error("Failed to get balance from PumpPortal trader.")
                return 0.0
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return 0.0

    async def get_wallet_balance(self) -> float:
        """Get the actual wallet balance (always real, regardless of mode) with intelligent routing."""
        try:
            # ENTERPRISE FIX: Intelligent routing for real wallet balance
            # Try unified trader first for network-aware balance checking
            if hasattr(self, 'unified_trader') and self.unified_trader and self._unified_trader_initialized:
                # For real wallet balance, we need to check the appropriate trader based on network mode
                trader_info = self.unified_trader.get_current_trader_info()
                if trader_info['supports_real_transactions']:
                    logger.debug(f"Using unified trader for wallet balance check (mode: {trader_info['network_mode']})")
                    balance = await self.unified_trader.get_balance()
                    if balance is not None and balance >= 0:
                        return balance
                    else:
                        logger.warning("Unified trader returned invalid balance, falling back to PumpPortal")

            # Graceful fallback to legacy PumpPortal (preserves existing functionality)
            if hasattr(self, 'pumpportal_trader') and self.pumpportal_trader:
                logger.debug("Using PumpPortal trader for wallet balance check (legacy mode)")
                balance = await self.pumpportal_trader.get_balance()
                return balance if balance is not None else 0.0
            else:
                logger.warning("No trader available for wallet balance check")
                return 0.0
        except Exception as e:
            logger.error(f"Error getting wallet balance: {e}")
            return 0.0

    def get_trading_balance(self) -> float:
        """Get the trading balance (real wallet balance only)."""
        # Real trading only - return actual wallet balance
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're in an async context, we can't await here
                # Return the last known balance from state as fallback
                return self.state.available_sol
            else:
                # If not in async context, get real balance
                return loop.run_until_complete(self.get_balance())
        except Exception as e:
            logger.warning(f"Error getting real balance, using state balance: {e}")
            return self.state.available_sol

    def get_starting_balance(self) -> float:
        """Get the starting balance (virtual balance used for profit calculations)."""
        return self.state.starting_sol

    async def send_balance_update(self, reason: str = "Periodic Update") -> bool:
        """
        Send a balance update message to Telegram.

        Args:
            reason: The reason for the balance update (e.g., "After Buy", "After Sell", "Periodic Update")

        Returns:
            bool: True if the message was sent successfully, False otherwise
        """
        try:
            # SURGICAL FIX: Always use trading balances for portfolio tracking
            # This ensures consistent profit/loss calculations regardless of mode
            current_balance = self.get_trading_balance()
            starting_balance = self.get_starting_balance()

            balance_change = current_balance - starting_balance
            change_percentage = (balance_change / starting_balance) * 100 if starting_balance > 0 else 0

            # Determine emoji based on balance change
            if balance_change > 0:
                emoji = "📈"
                change_text = f"+{balance_change:.4f} SOL (+{change_percentage:.2f}%)"
            elif balance_change < 0:
                emoji = "📉"
                change_text = f"{balance_change:.4f} SOL ({change_percentage:.2f}%)"
            else:
                emoji = "➖"
                change_text = "No change"

            # Create balance update message with the requested format (real trading only)
            balance_type = "Trading"  # Real trading only
            message = f"""💼 PORTFOLIO UPDATE – {reason.upper()}
━━━━━━━━━━━━━━━━━━━━━━━━━━━
📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔁 Trade: {self.run_mode}

💰 Starting {balance_type} Balance: {starting_balance:.4f} SOL
💸 Current {balance_type} Balance: {current_balance:.4f} SOL
📌 Net Change: {emoji} {change_text}"""

            # Send the message with multiple attempts
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    result = await self.signal_handler.send_info_message(message)
                    if result:
                        logger.info(f"Balance update sent successfully: {current_balance:.4f} SOL ({change_text})")
                        return True
                    else:
                        logger.warning(f"Balance update send returned False on attempt {attempt+1}")
                        if attempt < max_attempts - 1:
                            await asyncio.sleep(2)  # Wait before retrying
                except Exception as e:
                    logger.error(f"Error sending balance update (attempt {attempt+1}): {e}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(2)  # Wait before retrying

            # If we get here, all attempts failed
            logger.error(f"Failed to send balance update after {max_attempts} attempts")
            return False

        except Exception as e:
            logger.error(f"Error preparing balance update: {e}")
            return False

    # --- End Placeholder Methods ---
